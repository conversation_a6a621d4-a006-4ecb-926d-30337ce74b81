import Image from "next/image";
import { getImageUrl } from "@/lib/placeholder-images";

export default function AboutPage() {
  return (
    <div className="container mx-auto px-4 py-12">
      {/* Hero Section */}
      <div className="relative h-[300px] mb-12 rounded-lg overflow-hidden">
        <div className="absolute inset-0 bg-black/40 z-10"></div>
        <Image
          src={getImageUrl('about-hero')}
          alt="K-Plush Workshop"
          fill
          className="object-cover"
        />
        <div className="absolute inset-0 flex items-center z-20">
          <div className="container mx-auto px-4">
            <h1 className="text-4xl font-bold text-white">About K-Plush</h1>
          </div>
        </div>
      </div>

      {/* Our Story */}
      <div className="max-w-4xl mx-auto mb-16">
        <h2 className="text-3xl font-bold text-foreground mb-6">Our Story</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
          <div>
            <p className="text-foreground/70 mb-4">
              K Plush Group Ltd was founded in 2022, and we have accomplished so much over the years. To create a world where we provide quality in every single detail as our goal and slogan.
            </p>
            <p className="text-foreground/70 mb-4">
              Our Founder and CEO Dieudonne Niyigena (Kelly) was inspired to start this company by Art and Creativity. At K Plush Group Ltd, we encourage our community to join the group of satisfied customers.
            </p>
            <p className="text-foreground/70">
              Our passion for interior design and furniture craftsmanship drives everything we do. We believe that furniture should not only be functional but also enhance the beauty of your space and create lasting memories.
            </p>
          </div>
          <div className="relative h-[300px] rounded-lg overflow-hidden">
            <Image
              src={getImageUrl('workshop')}
              alt="K-Plush Workshop"
              fill
              className="object-cover"
            />
          </div>
        </div>
      </div>

      {/* Mission, Vision & Values */}
      <div className="bg-kgray-light dark:bg-kgray-medium py-16 px-4 rounded-lg mb-16">
        <div className="max-w-4xl mx-auto">
          {/* Mission & Vision */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            <div className="text-center">
              <div className="bg-primary h-16 w-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">Our Mission</h3>
              <p className="text-foreground/70">
                Our goal is to provide our customers with the best service and product at the best possible market price without compromising quality.
              </p>
            </div>

            <div className="text-center">
              <div className="bg-accent h-16 w-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">Our Vision</h3>
              <p className="text-foreground/70">
                To be the most reliable service and product provider and enhance the beauty of our space.
              </p>
            </div>
          </div>

          {/* Core Values */}
          <h2 className="text-3xl font-bold text-foreground mb-8 text-center">Our Core Values</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="text-center p-4">
              <div className="bg-primary/10 h-12 w-12 rounded-full flex items-center justify-center mx-auto mb-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h4 className="font-semibold text-foreground mb-2">Relationships</h4>
              <p className="text-foreground/70 text-sm">Relationships drive our business</p>
            </div>

            <div className="text-center p-4">
              <div className="bg-primary/10 h-12 w-12 rounded-full flex items-center justify-center mx-auto mb-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h4 className="font-semibold text-foreground mb-2">Integrity</h4>
              <p className="text-foreground/70 text-sm">Integrity through accountable behaviors</p>
            </div>

            <div className="text-center p-4">
              <div className="bg-primary/10 h-12 w-12 rounded-full flex items-center justify-center mx-auto mb-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <h4 className="font-semibold text-foreground mb-2">Respect</h4>
              <p className="text-foreground/70 text-sm">Respect earned through our actions</p>
            </div>

            <div className="text-center p-4">
              <div className="bg-primary/10 h-12 w-12 rounded-full flex items-center justify-center mx-auto mb-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h4 className="font-semibold text-foreground mb-2">Innovation</h4>
              <p className="text-foreground/70 text-sm">Innovative thinking for new solutions</p>
            </div>

            <div className="text-center p-4 md:col-span-2 lg:col-span-1">
              <div className="bg-primary/10 h-12 w-12 rounded-full flex items-center justify-center mx-auto mb-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
              <h4 className="font-semibold text-foreground mb-2">Teamwork</h4>
              <p className="text-foreground/70 text-sm">Teamwork for extraordinary results</p>
            </div>
          </div>
        </div>
      </div>

      {/* Our Services */}
      <div className="max-w-4xl mx-auto mb-16">
        <h2 className="text-3xl font-bold text-foreground mb-8 text-center">Our Services</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center bg-white dark:bg-gray-700/80 p-6 rounded-xl shadow-sm">
            <div className="bg-primary/10 h-16 w-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-foreground mb-3">Interior Design</h3>
            <p className="text-foreground/70 text-sm">
              Complete interior design services for homes, offices, hotels, restaurants, bars, and hair salons.
            </p>
          </div>

          <div className="text-center bg-white dark:bg-gray-700/80 p-6 rounded-xl shadow-sm">
            <div className="bg-primary/10 h-16 w-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-foreground mb-3">All Furniture</h3>
            <p className="text-foreground/70 text-sm">
              Custom and ready-made furniture with all designs to suit your style and space requirements.
            </p>
          </div>

          <div className="text-center bg-white dark:bg-gray-700/80 p-6 rounded-xl shadow-sm">
            <div className="bg-primary/10 h-16 w-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4 4 4 0 004-4V5z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-foreground mb-3">Décor Products</h3>
            <p className="text-foreground/70 text-sm">
              Beautiful decorative products including vases and flowers to complete your interior design.
            </p>
          </div>
        </div>
      </div>

      {/* Leadership */}
      <div className="max-w-4xl mx-auto mb-16">
        <h2 className="text-3xl font-bold text-foreground mb-8 text-center">Leadership</h2>
        <div className="flex justify-center">
          <div className="text-center bg-white dark:bg-gray-700/80 p-8 rounded-xl shadow-sm max-w-md">
            <div className="relative h-32 w-32 rounded-full overflow-hidden mx-auto mb-6 bg-primary/10 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-foreground mb-2">Dieudonne Niyigena (Kelly)</h3>
            <p className="text-primary mb-3 font-medium">Founder & CEO</p>
            <p className="text-foreground/70 text-sm">
              Inspired by art and creativity, Kelly founded K Plush Group Ltd with a vision to provide quality in every single detail and enhance the beauty of spaces across Rwanda.
            </p>
          </div>
        </div>
      </div>

      {/* Our Location */}
      <div className="max-w-4xl mx-auto">
        <h2 className="text-3xl font-bold text-foreground mb-6 text-center">Visit Our Showroom</h2>
        <div className="bg-white dark:bg-gray-700/80 p-8 rounded-xl shadow-sm">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
            <div>
              <h3 className="text-xl font-semibold text-foreground mb-4">Find Us in Kigali</h3>
              <div className="space-y-3">
                <div className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary mr-3 mt-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  <div>
                    <p className="text-foreground font-medium">Address</p>
                    <p className="text-foreground/70 text-sm">KG 11 Ave, Gasabo Kigali – Rwanda</p>
                    <p className="text-foreground/70 text-sm">Kimironko, Opposite K.I.E University</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary mr-3 mt-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                  <div>
                    <p className="text-foreground font-medium">Phone</p>
                    <p className="text-foreground/70 text-sm">+250 793 898 899</p>
                    <p className="text-foreground/70 text-sm">+250 727 193 570</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary mr-3 mt-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  <div>
                    <p className="text-foreground font-medium">Email</p>
                    <p className="text-foreground/70 text-sm"><EMAIL></p>
                  </div>
                </div>
              </div>
            </div>

            <div className="relative h-64 rounded-lg overflow-hidden">
              <Image
                src={getImageUrl('showroom')}
                alt="K-Plush Showroom in Kimironko"
                fill
                className="object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"></div>
              <div className="absolute bottom-4 left-4 text-white">
                <p className="font-medium">Our Showroom</p>
                <p className="text-sm opacity-90">Kimironko, Kigali</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
