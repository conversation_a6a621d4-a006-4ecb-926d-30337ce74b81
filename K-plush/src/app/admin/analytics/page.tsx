'use client';

import {
  ChartBarIcon,
  ArrowTrendingUpIcon,
  UsersIcon,
  ShoppingBagIcon,
  CurrencyDollarIcon,
  EyeIcon
} from '@heroicons/react/24/outline';

// Mock analytics data
const analyticsData = {
  overview: [
    {
      name: 'Total Revenue',
      value: 'RWF 12,450,000',
      change: '+23.5%',
      changeType: 'increase',
      icon: CurrencyDollarIcon,
      color: 'from-green-500 to-emerald-600',
    },
    {
      name: 'Orders This Month',
      value: '89',
      change: '+18.2%',
      changeType: 'increase',
      icon: ShoppingBagIcon,
      color: 'from-blue-500 to-blue-600',
    },
    {
      name: 'New Customers',
      value: '34',
      change: '+12.1%',
      changeType: 'increase',
      icon: UsersIcon,
      color: 'from-purple-500 to-purple-600',
    },
    {
      name: 'Website Views',
      value: '2,847',
      change: '+8.7%',
      changeType: 'increase',
      icon: EyeIcon,
      color: 'from-orange-500 to-orange-600',
    },
  ],
  topProducts: [
    { name: 'Modern Living Room Set', sales: 23, revenue: 'RWF 2,300,000' },
    { name: 'Executive Office Desk', sales: 18, revenue: 'RWF 1,800,000' },
    { name: 'Luxury Bedroom Suite', sales: 15, revenue: 'RWF 2,250,000' },
    { name: 'Dining Table Set', sales: 12, revenue: 'RWF 1,200,000' },
    { name: 'Custom Wardrobe', sales: 8, revenue: 'RWF 1,600,000' },
  ],
  recentActivity: [
    { action: 'New order received', customer: 'Jean Baptiste', time: '2 minutes ago', type: 'order' },
    { action: 'Custom design request', customer: 'Marie Claire', time: '15 minutes ago', type: 'design' },
    { action: 'Payment received', customer: 'Patrick Uwimana', time: '1 hour ago', type: 'payment' },
    { action: 'Product delivered', customer: 'Grace Mukamana', time: '3 hours ago', type: 'delivery' },
    { action: 'New customer registered', customer: 'David Nkurunziza', time: '5 hours ago', type: 'customer' },
  ]
};

export default function AnalyticsPage() {
  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="bg-white dark:bg-kgray-dark rounded-2xl p-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">
              Analytics Dashboard 📊
            </h1>
            <p className="text-foreground/70 text-lg">
              Track your business performance and insights
            </p>
          </div>
          <div className="hidden md:block">
            <div className="w-20 h-20 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-2xl flex items-center justify-center">
              <ChartBarIcon className="h-10 w-10 text-blue-500" />
            </div>
          </div>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {analyticsData.overview.map((stat) => (
          <div
            key={stat.name}
            className="bg-white dark:bg-kgray-dark rounded-2xl p-6 transition-all duration-200"
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-foreground/60 mb-2">
                  {stat.name}
                </p>
                <p className="text-2xl font-bold text-foreground mb-3">
                  {stat.value}
                </p>
                <div className="flex items-center">
                  <ArrowTrendingUpIcon className="h-4 w-4 text-green-500 mr-1" />
                  <span className="text-sm font-semibold text-green-600 dark:text-green-400">
                    {stat.change}
                  </span>
                  <span className="text-sm text-foreground/50 ml-1">vs last month</span>
                </div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-900/20 p-3 rounded-xl">
                <div className={`w-8 h-8 bg-gradient-to-br ${stat.color} rounded-lg flex items-center justify-center`}>
                  <stat.icon className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts and Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Top Products */}
        <div className="bg-white dark:bg-kgray-dark rounded-2xl">
          <div className="px-6 py-4">
            <h3 className="text-lg font-semibold text-foreground">Top Selling Products</h3>
            <p className="text-sm text-foreground/60 mt-1">Best performing products this month</p>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {analyticsData.topProducts.map((product, index) => (
                <div key={product.name} className="flex items-center justify-between p-4 rounded-xl bg-kgray-light/30 dark:bg-kgray-medium/30">
                  <div className="flex items-center space-x-4">
                    <div className="w-8 h-8 bg-gradient-to-br from-primary/20 to-accent/20 rounded-lg flex items-center justify-center">
                      <span className="text-sm font-bold text-primary">#{index + 1}</span>
                    </div>
                    <div>
                      <h4 className="text-sm font-semibold text-foreground">{product.name}</h4>
                      <p className="text-xs text-foreground/60">{product.sales} sales</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-bold text-foreground">{product.revenue}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white dark:bg-kgray-dark rounded-2xl">
          <div className="px-6 py-4">
            <h3 className="text-lg font-semibold text-foreground">Recent Activity</h3>
            <p className="text-sm text-foreground/60 mt-1">Latest business activities</p>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {analyticsData.recentActivity.map((activity, index) => (
                <div key={index} className="flex items-start space-x-4 p-4 rounded-xl hover:bg-kgray-light/30 dark:hover:bg-kgray-medium/30 transition-colors">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    activity.type === 'order' ? 'bg-blue-100 dark:bg-blue-900/20' :
                    activity.type === 'design' ? 'bg-purple-100 dark:bg-purple-900/20' :
                    activity.type === 'payment' ? 'bg-green-100 dark:bg-green-900/20' :
                    activity.type === 'delivery' ? 'bg-orange-100 dark:bg-orange-900/20' :
                    'bg-gray-100 dark:bg-gray-900/20'
                  }`}>
                    <div className={`w-3 h-3 rounded-full ${
                      activity.type === 'order' ? 'bg-blue-500' :
                      activity.type === 'design' ? 'bg-purple-500' :
                      activity.type === 'payment' ? 'bg-green-500' :
                      activity.type === 'delivery' ? 'bg-orange-500' :
                      'bg-gray-500'
                    }`}></div>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-foreground">{activity.action}</p>
                    <p className="text-xs text-foreground/60">{activity.customer} • {activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
