'use client';

import { useState, use } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { 
  ArrowLeftIcon,
  ChatBubbleLeftIcon,
  PaperClipIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  CurrencyDollarIcon,
  DocumentTextIcon,
  PhotoIcon
} from '@heroicons/react/24/outline';

// Mock data for a custom design request
const customDesignData = {
  id: 'CDR-001',
  title: 'Custom Teddy Bear with Embroidery',
  customer: {
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    avatar: '/images/team/placeholder.jpg'
  },
  status: 'pending',
  priority: 'medium',
  date: '2024-01-15T10:30:00',
  description: 'I would like a custom teddy bear with my daughter\'s name "<PERSON>" embroidered on the paw. The teddy bear should be light brown with a cream-colored belly. I prefer a size of about 16 inches tall. The embroidery should be in pink color and in a cursive font if possible. This is for her 5th birthday next month.',
  budget: 199.99,
  timeline: '3 weeks',
  attachments: [
    {
      id: 1,
      name: 'reference_teddy.jpg',
      type: 'image/jpeg',
      size: '1.2 MB',
      url: '/images/products/placeholder.jpg'
    },
    {
      id: 2,
      name: 'embroidery_font_example.pdf',
      type: 'application/pdf',
      size: '450 KB',
      url: '#'
    }
  ],
  messages: [
    {
      id: 1,
      sender: 'customer',
      content: 'Hello, I\'m interested in getting a custom teddy bear made for my daughter\'s birthday.',
      date: '2024-01-15T10:30:00'
    },
    {
      id: 2,
      sender: 'admin',
      content: 'Hi John, thank you for your interest! We\'d be happy to create a custom teddy bear for your daughter. Could you provide more details about what you\'re looking for?',
      date: '2024-01-15T11:15:00'
    },
    {
      id: 3,
      sender: 'customer',
      content: 'I\'d like a light brown teddy bear with her name "Emma" embroidered on the paw in pink. I\'ve attached a reference image of the style I like.',
      date: '2024-01-15T11:45:00'
    }
  ],
  notes: [
    {
      id: 1,
      author: 'Admin',
      content: 'Customer mentioned this is for a birthday gift, so we should prioritize this order to ensure delivery on time.',
      date: '2024-01-15T13:20:00'
    }
  ]
};

// Status badge component
const StatusBadge = ({ status }: { status: string }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-amber-50 text-amber-700 border-amber-200';
      case 'in_progress':
        return 'bg-blue-50 text-blue-700 border-blue-200';
      case 'approved':
        return 'bg-green-50 text-green-700 border-green-200';
      case 'completed':
        return 'bg-indigo-50 text-indigo-700 border-indigo-200';
      case 'rejected':
        return 'bg-red-50 text-red-700 border-red-200';
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'in_progress':
        return 'In Progress';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  return (
    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-md border ${getStatusColor(status)}`}>
      {getStatusLabel(status)}
    </span>
  );
};

// Priority badge component
const PriorityBadge = ({ priority }: { priority: string }) => {
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-50 text-red-700 border-red-200';
      case 'medium':
        return 'bg-amber-50 text-amber-700 border-amber-200';
      case 'low':
        return 'bg-green-50 text-green-700 border-green-200';
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };

  return (
    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-md border ${getPriorityColor(priority)}`}>
      {priority.charAt(0).toUpperCase() + priority.slice(1)}
    </span>
  );
};

export default function CustomDesignDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = use(params);
  const [activeTab, setActiveTab] = useState('details');
  const [currentStatus, setCurrentStatus] = useState(customDesignData.status);
  const [showStatusConfirmation, setShowStatusConfirmation] = useState<string | null>(null);
  const [note, setNote] = useState('');

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  // Handle status change
  const handleStatusChange = (status: string) => {
    setCurrentStatus(status);
    setShowStatusConfirmation(null);
    // In a real app, you would call an API to update the status
  };

  // Add note
  const handleAddNote = () => {
    if (note.trim()) {
      // In a real app, you would add the note to your database
      // For now, we'll just clear the input
      setNote('');
    }
  };

  // Get file icon based on type
  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) {
      return <PhotoIcon className="h-6 w-6 text-blue-500" />;
    } else if (fileType.startsWith('application/pdf')) {
      return <DocumentTextIcon className="h-6 w-6 text-red-500" />;
    } else {
      return <PaperClipIcon className="h-6 w-6 text-gray-500" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center">
          <Link href="/admin/custom-designs" className="mr-4 p-1 rounded-full hover:bg-gray-100">
            <ArrowLeftIcon className="h-5 w-5 text-gray-500" />
          </Link>
          <div>
            <div className="flex items-center gap-3">
              <h1 className="text-2xl font-medium text-gray-900">
                {customDesignData.title}
              </h1>
              <StatusBadge status={currentStatus} />
            </div>
            <p className="text-sm text-gray-500 mt-1">
              Request {id} • Submitted on {formatDate(customDesignData.date)}
            </p>
          </div>
        </div>
        <div className="flex flex-wrap gap-3">
          {currentStatus === 'pending' && (
            <>
              <button 
                onClick={() => setShowStatusConfirmation('approved')}
                className="flex items-center px-4 py-2 bg-green-600 rounded-md text-sm font-medium text-white hover:bg-green-700"
              >
                <CheckCircleIcon className="h-4 w-4 mr-1.5" />
                Approve
              </button>
              <button 
                onClick={() => setShowStatusConfirmation('rejected')}
                className="flex items-center px-4 py-2 bg-red-600 rounded-md text-sm font-medium text-white hover:bg-red-700"
              >
                <XCircleIcon className="h-4 w-4 mr-1.5" />
                Reject
              </button>
            </>
          )}
          <Link 
            href={`/admin/custom-designs/${id}/messages`}
            className="flex items-center px-4 py-2 border border-gray-200 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            <ChatBubbleLeftIcon className="h-4 w-4 mr-1.5" />
            Messages
          </Link>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('details')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'details'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Details
          </button>
          <button
            onClick={() => setActiveTab('attachments')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'attachments'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Attachments
          </button>
          <button
            onClick={() => setActiveTab('notes')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'notes'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Notes
          </button>
        </nav>
      </div>

      {/* Content based on active tab */}
      <div className="space-y-6">
        {activeTab === 'details' && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Request Details */}
            <div className="md:col-span-2 bg-white rounded-lg border border-gray-100 p-6">
              <h3 className="text-sm font-medium text-gray-900 mb-4">Request Details</h3>
              <div className="prose prose-sm max-w-none text-gray-700">
                <p>{customDesignData.description}</p>
              </div>
              
              <div className="mt-6 grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-xs font-medium text-gray-500 uppercase">Status</h4>
                  <p className="mt-1">
                    <StatusBadge status={currentStatus} />
                  </p>
                </div>
                <div>
                  <h4 className="text-xs font-medium text-gray-500 uppercase">Priority</h4>
                  <p className="mt-1">
                    <PriorityBadge priority={customDesignData.priority} />
                  </p>
                </div>
                <div>
                  <h4 className="text-xs font-medium text-gray-500 uppercase">Budget</h4>
                  <p className="mt-1 flex items-center text-sm text-gray-900">
                    <CurrencyDollarIcon className="h-4 w-4 text-gray-500 mr-1" />
                    ${customDesignData.budget.toFixed(2)}
                  </p>
                </div>
                <div>
                  <h4 className="text-xs font-medium text-gray-500 uppercase">Timeline</h4>
                  <p className="mt-1 flex items-center text-sm text-gray-900">
                    <ClockIcon className="h-4 w-4 text-gray-500 mr-1" />
                    {customDesignData.timeline}
                  </p>
                </div>
              </div>
            </div>

            {/* Customer Information */}
            <div className="bg-white rounded-lg border border-gray-100 p-6">
              <h3 className="text-sm font-medium text-gray-900 mb-4">Customer Information</h3>
              <div className="flex items-center mb-4">
                <div className="h-10 w-10 rounded-full overflow-hidden relative">
                  <Image
                    src={customDesignData.customer.avatar}
                    alt={customDesignData.customer.name}
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-900">{customDesignData.customer.name}</p>
                  <p className="text-xs text-gray-500">{customDesignData.customer.email}</p>
                </div>
              </div>
              <div className="space-y-2 text-sm">
                <p className="flex items-center text-gray-700">
                  <span className="w-20 text-gray-500">Email:</span>
                  <a href={`mailto:${customDesignData.customer.email}`} className="text-blue-600 hover:underline">
                    {customDesignData.customer.email}
                  </a>
                </p>
                <p className="flex items-center text-gray-700">
                  <span className="w-20 text-gray-500">Phone:</span>
                  <a href={`tel:${customDesignData.customer.phone}`} className="text-blue-600 hover:underline">
                    {customDesignData.customer.phone}
                  </a>
                </p>
              </div>
              <div className="mt-4 pt-4 border-t border-gray-100">
                <Link 
                  href={`/admin/custom-designs/${id}/messages`}
                  className="flex items-center text-sm text-blue-600 hover:text-blue-800"
                >
                  <ChatBubbleLeftIcon className="h-4 w-4 mr-1.5" />
                  View Conversation History
                </Link>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'attachments' && (
          <div className="bg-white rounded-lg border border-gray-100 p-6">
            <h3 className="text-sm font-medium text-gray-900 mb-4">Attachments</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {customDesignData.attachments.map((attachment) => (
                <div key={attachment.id} className="border border-gray-200 rounded-md p-4 flex items-start">
                  <div className="flex-shrink-0">
                    {getFileIcon(attachment.type)}
                  </div>
                  <div className="ml-3 flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {attachment.name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {attachment.size}
                    </p>
                  </div>
                  <div className="ml-4 flex-shrink-0">
                    <a href={attachment.url} target="_blank" rel="noopener noreferrer" className="text-sm font-medium text-blue-600 hover:text-blue-800">
                      View
                    </a>
                  </div>
                </div>
              ))}
            </div>
            
            {customDesignData.attachments.length === 0 && (
              <div className="text-center py-12">
                <PaperClipIcon className="mx-auto h-12 w-12 text-gray-300" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No attachments</h3>
                <p className="mt-1 text-sm text-gray-500">This request doesn&apos;t have any attachments.</p>
              </div>
            )}
            
            <div className="mt-6">
              <button className="px-4 py-2 border border-gray-200 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                Add Attachment
              </button>
            </div>
          </div>
        )}

        {activeTab === 'notes' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg border border-gray-100">
              <div className="px-6 py-4 border-b border-gray-100">
                <h3 className="text-base font-medium text-gray-900">
                  Internal Notes
                </h3>
              </div>
              <div className="p-6">
                <div className="flow-root">
                  <ul className="-mb-8">
                    {customDesignData.notes.map((noteItem, noteIdx) => (
                      <li key={noteItem.id}>
                        <div className="relative pb-8">
                          {noteIdx !== customDesignData.notes.length - 1 ? (
                            <span className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true" />
                          ) : null}
                          <div className="relative flex space-x-3">
                            <div>
                              <span className="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center">
                                <ChatBubbleLeftIcon className="h-4 w-4 text-gray-500" />
                              </span>
                            </div>
                            <div className="min-w-0 flex-1">
                              <div className="text-sm text-gray-500">
                                <span className="font-medium text-gray-900">{noteItem.author}</span> added a note
                              </div>
                              <div className="mt-2 text-sm text-gray-700">
                                <p>{noteItem.content}</p>
                              </div>
                              <div className="mt-1 text-xs text-gray-500">
                                {formatDate(noteItem.date)}
                              </div>
                            </div>
                          </div>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
                
                {customDesignData.notes.length === 0 && (
                  <div className="text-center py-12">
                    <ChatBubbleLeftIcon className="mx-auto h-12 w-12 text-gray-300" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No notes</h3>
                    <p className="mt-1 text-sm text-gray-500">There are no internal notes for this request yet.</p>
                  </div>
                )}
              </div>
            </div>

            {/* Add Note Form */}
            <div className="bg-white rounded-lg border border-gray-100 p-4">
              <h3 className="text-sm font-medium text-gray-900 mb-3">Add Note</h3>
              <div className="space-y-3">
                <textarea
                  value={note}
                  onChange={(e) => setNote(e.target.value)}
                  rows={3}
                  className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Add an internal note about this request..."
                />
                <div className="flex justify-end">
                  <button
                    onClick={handleAddNote}
                    disabled={!note.trim()}
                    className={`px-4 py-2 rounded-md text-sm font-medium ${
                      note.trim()
                        ? 'bg-blue-600 text-white hover:bg-blue-700'
                        : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    }`}
                  >
                    Add Note
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Status Change Confirmation Modals */}
      {showStatusConfirmation === 'approved' && (
        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 sm:mx-0 sm:h-10 sm:w-10">
                    <CheckCircleIcon className="h-6 w-6 text-green-600" aria-hidden="true" />
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      Approve Request
                    </h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        Are you sure you want to approve this custom design request? This will change the status to &quot;Approved&quot; and notify the customer.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  onClick={() => handleStatusChange('approved')}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Approve
                </button>
                <button
                  type="button"
                  onClick={() => setShowStatusConfirmation(null)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {showStatusConfirmation === 'rejected' && (
        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <XCircleIcon className="h-6 w-6 text-red-600" aria-hidden="true" />
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      Reject Request
                    </h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        Are you sure you want to reject this custom design request? This will change the status to &quot;Rejected&quot; and notify the customer.
                      </p>
                      <div className="mt-4">
                        <label htmlFor="rejection-reason" className="block text-sm font-medium text-gray-700">
                          Reason for rejection (optional)
                        </label>
                        <textarea
                          id="rejection-reason"
                          rows={3}
                          className="mt-1 block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500"
                          placeholder="Provide a reason for rejecting this request..."
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  onClick={() => handleStatusChange('rejected')}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Reject
                </button>
                <button
                  type="button"
                  onClick={() => setShowStatusConfirmation(null)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 