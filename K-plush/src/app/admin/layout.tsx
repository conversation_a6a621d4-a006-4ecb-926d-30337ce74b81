import { Metadata } from 'next/types';
import "../globals.css";
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';

export const metadata: Metadata = {
  title: 'Admin Dashboard - K-Plush',
  description: 'K-Plush Admin Dashboard for managing products, orders, and users',
};

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen bg-kgray-light dark:bg-kgray-dark">
      <div className="flex">
        {/* Sidebar */}
        <AdminSidebar />

        {/* Main Content */}
        <div className="flex-1 flex flex-col min-h-screen ml-64">
          <AdminHeader />
          <main className="flex-1 px-4 py-6 w-full">
            {children}
          </main>
        </div>
      </div>
    </div>
  );
}