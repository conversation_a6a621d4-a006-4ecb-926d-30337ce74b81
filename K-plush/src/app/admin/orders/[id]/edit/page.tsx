'use client';

import { useState, useEffect, use } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { 
  ArrowLeftIcon, 
  PlusIcon,
  TrashIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

// Mock order data - in a real app, this would be fetched from an API
const orderData = {
  id: 'ORD-001',
  date: '2024-01-15T10:30:00',
  status: 'processing',
  customer: {
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    address: {
      street: '123 Main St',
      city: 'New York',
      state: 'NY',
      zip: '10001',
      country: 'United States'
    }
  },
  payment: {
    method: 'Credit Card',
    cardLast4: '4242',
    status: 'paid',
    total: 299.99,
    subtotal: 279.99,
    tax: 20.00,
    shipping: 0,
    discount: 0
  },
  shipping: {
    method: 'Standard Shipping',
    trackingNumber: 'TRK123456789',
    carrier: 'UPS',
    estimatedDelivery: '2024-01-20'
  },
  items: [
    {
      id: 1,
      name: '<PERSON> Bear (Large)',
      sku: 'TB-LRG-001',
      price: 149.99,
      quantity: 1,
      image: '/images/products/placeholder.jpg'
    },
    {
      id: 2,
      name: 'Plush Elephant',
      sku: 'PE-MED-002',
      price: 130.00,
      quantity: 1,
      image: '/images/products/placeholder.jpg'
    }
  ]
};

// Mock products for the product selector
const products = [
  { id: 1, name: 'Teddy Bear (Large)', sku: 'TB-LRG-001', price: 149.99, image: '/images/products/placeholder.jpg' },
  { id: 2, name: 'Plush Elephant', sku: 'PE-MED-002', price: 130.00, image: '/images/products/placeholder.jpg' },
  { id: 3, name: 'Plush Lion', sku: 'PL-MED-003', price: 135.00, image: '/images/products/placeholder.jpg' },
  { id: 4, name: 'Teddy Bear (Small)', sku: 'TB-SML-004', price: 89.99, image: '/images/products/placeholder.jpg' },
  { id: 5, name: 'Plush Giraffe', sku: 'PG-LRG-005', price: 159.99, image: '/images/products/placeholder.jpg' },
];

export default function OrderEditPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = use(params);
  // State for form data
  const [formData, setFormData] = useState({
    status: orderData.status,
    customer: { ...orderData.customer },
    customerAddress: { ...orderData.customer.address },
    shipping: { ...orderData.shipping },
    payment: { ...orderData.payment },
    items: [...orderData.items],
  });

  // State for product selector
  const [showProductSelector, setShowProductSelector] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredProducts, setFilteredProducts] = useState(products);

  // Calculate totals
  const [totals, setTotals] = useState({
    subtotal: 0,
    tax: formData.payment.tax,
    shipping: formData.payment.shipping,
    discount: formData.payment.discount,
    total: 0,
  });

  // Update filtered products when search query changes
  useEffect(() => {
    setFilteredProducts(
      products.filter(product => 
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.sku.toLowerCase().includes(searchQuery.toLowerCase())
      )
    );
  }, [searchQuery]);

  // Recalculate totals when items change
  useEffect(() => {
    const subtotal = formData.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const tax = formData.payment.tax;
    const shipping = formData.payment.shipping;
    const discount = formData.payment.discount;
    const total = subtotal + tax + shipping - discount;
    
    setTotals({
      subtotal,
      tax,
      shipping,
      discount,
      total,
    });
  }, [formData.items, formData.payment.tax, formData.payment.shipping, formData.payment.discount]);

  // Handle input changes
  const handleInputChange = (section: string, field: string, value: string | number) => {
    if (section === 'status') {
      setFormData({
        ...formData,
        status: value as string,
      });
    } else if (section === 'customer') {
      setFormData({
        ...formData,
        customer: {
          ...formData.customer,
          [field]: value,
        },
      });
    } else if (section === 'shipping') {
      setFormData({
        ...formData,
        shipping: {
          ...formData.shipping,
          [field]: value,
        },
      });
    } else if (section === 'payment') {
      setFormData({
        ...formData,
        payment: {
          ...formData.payment,
          [field]: value,
        },
      });
    }
  };

  // Handle address changes
  const handleAddressChange = (field: string, value: string) => {
    setFormData({
      ...formData,
      customerAddress: {
        ...formData.customerAddress,
        [field]: value,
      },
    });
  };

  // Handle item quantity change
  const handleItemQuantityChange = (id: number, quantity: number) => {
    setFormData({
      ...formData,
      items: formData.items.map(item => 
        item.id === id ? { ...item, quantity } : item
      ),
    });
  };

  // Handle item removal
  const handleRemoveItem = (id: number) => {
    setFormData({
      ...formData,
      items: formData.items.filter(item => item.id !== id),
    });
  };

  // Handle adding a product
  const handleAddProduct = (product: typeof products[0]) => {
    // Check if product already exists in the order
    const existingItem = formData.items.find(item => item.id === product.id);
    
    if (existingItem) {
      // Update quantity if product already exists
      handleItemQuantityChange(product.id, existingItem.quantity + 1);
    } else {
      // Add new product with quantity 1
      setFormData({
        ...formData,
        items: [
          ...formData.items,
          { ...product, quantity: 1 },
        ],
      });
    }
    
    // Close product selector
    setShowProductSelector(false);
    setSearchQuery('');
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Combine all form data
    const updatedOrder = {
      ...orderData,
      status: formData.status,
      customer: {
        ...formData.customer,
        address: formData.customerAddress,
      },
      shipping: formData.shipping,
      payment: {
        ...formData.payment,
        subtotal: totals.subtotal,
        total: totals.total,
      },
      items: formData.items,
    };
    
    // Here you would submit the updated order to your API
    console.log('Updated order:', updatedOrder);
    
    // Redirect to order details page
    window.location.href = `/admin/orders/${id}`;
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Link href={`/admin/orders/${id}`} className="mr-4 p-1 rounded-full hover:bg-gray-100">
            <ArrowLeftIcon className="h-5 w-5 text-gray-500" />
          </Link>
          <div>
            <h1 className="text-2xl font-medium text-gray-900">
              Edit Order {id}
            </h1>
            <p className="text-sm text-gray-500 mt-1">
              Update order details, customer information, and items
            </p>
          </div>
        </div>
        <div className="flex space-x-3">
          <Link
            href={`/admin/orders/${id}`}
            className="px-4 py-2 border border-gray-200 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </Link>
          <button
            onClick={handleSubmit}
            className="px-4 py-2 bg-blue-600 rounded-md text-sm font-medium text-white hover:bg-blue-700"
          >
            Save Changes
          </button>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Order Status */}
        <div className="bg-white rounded-lg border border-gray-100 p-4">
          <h3 className="text-sm font-medium text-gray-900 mb-3">Order Status</h3>
          <div className="w-full sm:w-64">
            <select
              value={formData.status}
              onChange={(e) => handleInputChange('status', 'status', e.target.value)}
              className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="pending">Pending</option>
              <option value="processing">Processing</option>
              <option value="shipped">Shipped</option>
              <option value="delivered">Delivered</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
        </div>

        {/* Customer Information */}
        <div className="bg-white rounded-lg border border-gray-100 p-4">
          <h3 className="text-sm font-medium text-gray-900 mb-3">Customer Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="customer-name" className="block text-xs font-medium text-gray-700 mb-1">
                Name
              </label>
              <input
                id="customer-name"
                type="text"
                value={formData.customer.name}
                onChange={(e) => handleInputChange('customer', 'name', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label htmlFor="customer-email" className="block text-xs font-medium text-gray-700 mb-1">
                Email
              </label>
              <input
                id="customer-email"
                type="email"
                value={formData.customer.email}
                onChange={(e) => handleInputChange('customer', 'email', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label htmlFor="customer-phone" className="block text-xs font-medium text-gray-700 mb-1">
                Phone
              </label>
              <input
                id="customer-phone"
                type="text"
                value={formData.customer.phone}
                onChange={(e) => handleInputChange('customer', 'phone', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          <h4 className="text-xs font-medium text-gray-700 mt-4 mb-2">Shipping Address</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <label htmlFor="address-street" className="block text-xs font-medium text-gray-700 mb-1">
                Street Address
              </label>
              <input
                id="address-street"
                type="text"
                value={formData.customerAddress.street}
                onChange={(e) => handleAddressChange('street', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label htmlFor="address-city" className="block text-xs font-medium text-gray-700 mb-1">
                City
              </label>
              <input
                id="address-city"
                type="text"
                value={formData.customerAddress.city}
                onChange={(e) => handleAddressChange('city', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label htmlFor="address-state" className="block text-xs font-medium text-gray-700 mb-1">
                State / Province
              </label>
              <input
                id="address-state"
                type="text"
                value={formData.customerAddress.state}
                onChange={(e) => handleAddressChange('state', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label htmlFor="address-zip" className="block text-xs font-medium text-gray-700 mb-1">
                ZIP / Postal Code
              </label>
              <input
                id="address-zip"
                type="text"
                value={formData.customerAddress.zip}
                onChange={(e) => handleAddressChange('zip', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label htmlFor="address-country" className="block text-xs font-medium text-gray-700 mb-1">
                Country
              </label>
              <input
                id="address-country"
                type="text"
                value={formData.customerAddress.country}
                onChange={(e) => handleAddressChange('country', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>

        {/* Shipping Information */}
        <div className="bg-white rounded-lg border border-gray-100 p-4">
          <h3 className="text-sm font-medium text-gray-900 mb-3">Shipping Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="shipping-method" className="block text-xs font-medium text-gray-700 mb-1">
                Shipping Method
              </label>
              <input
                id="shipping-method"
                type="text"
                value={formData.shipping.method}
                onChange={(e) => handleInputChange('shipping', 'method', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label htmlFor="shipping-carrier" className="block text-xs font-medium text-gray-700 mb-1">
                Carrier
              </label>
              <input
                id="shipping-carrier"
                type="text"
                value={formData.shipping.carrier}
                onChange={(e) => handleInputChange('shipping', 'carrier', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label htmlFor="shipping-tracking" className="block text-xs font-medium text-gray-700 mb-1">
                Tracking Number
              </label>
              <input
                id="shipping-tracking"
                type="text"
                value={formData.shipping.trackingNumber}
                onChange={(e) => handleInputChange('shipping', 'trackingNumber', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label htmlFor="shipping-delivery" className="block text-xs font-medium text-gray-700 mb-1">
                Estimated Delivery Date
              </label>
              <input
                id="shipping-delivery"
                type="date"
                value={formData.shipping.estimatedDelivery.split('T')[0]}
                onChange={(e) => handleInputChange('shipping', 'estimatedDelivery', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>

        {/* Order Items */}
        <div className="bg-white rounded-lg border border-gray-100">
          <div className="px-4 py-4 border-b border-gray-100 flex items-center justify-between">
            <h3 className="text-base font-medium text-gray-900">Order Items</h3>
            <button
              type="button"
              onClick={() => setShowProductSelector(true)}
              className="flex items-center px-3 py-1.5 bg-blue-50 border border-blue-200 rounded-md text-sm font-medium text-blue-600 hover:bg-blue-100"
            >
              <PlusIcon className="h-4 w-4 mr-1" />
              Add Product
            </button>
          </div>
          <div className="overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-100">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Product
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      SKU
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Price
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Quantity
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <span className="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-100">
                  {formData.items.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="h-10 w-10 flex-shrink-0 relative">
                            <Image
                              src={item.image}
                              alt={item.name}
                              fill
                              className="object-cover rounded-md"
                            />
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {item.name}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {item.sku}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                        ${item.price.toFixed(2)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right">
                        <div className="flex justify-end">
                          <input
                            type="number"
                            min="1"
                            value={item.quantity}
                            onChange={(e) => handleItemQuantityChange(item.id, parseInt(e.target.value) || 1)}
                            className="w-16 px-2 py-1 border border-gray-200 rounded-md text-sm text-right focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-right">
                        ${(item.price * item.quantity).toFixed(2)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right">
                        <button
                          type="button"
                          onClick={() => handleRemoveItem(item.id)}
                          className="text-gray-400 hover:text-red-600"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          
          {/* Order Summary */}
          <div className="px-6 py-4 border-t border-gray-100">
            <div className="flex flex-col items-end">
              <div className="w-full max-w-xs space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Subtotal</span>
                  <span className="text-gray-900">${totals.subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <div className="flex items-center">
                    <span className="text-gray-500">Tax</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-gray-900">${totals.tax.toFixed(2)}</span>
                  </div>
                </div>
                <div className="flex justify-between text-sm">
                  <div className="flex items-center">
                    <span className="text-gray-500">Shipping</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-gray-900">${totals.shipping.toFixed(2)}</span>
                  </div>
                </div>
                {totals.discount > 0 && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Discount</span>
                    <span className="text-red-600">-${totals.discount.toFixed(2)}</span>
                  </div>
                )}
                <div className="flex justify-between pt-2 border-t border-gray-100 text-base font-medium">
                  <span className="text-gray-900">Total</span>
                  <span className="text-gray-900">${totals.total.toFixed(2)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Submit Buttons */}
        <div className="flex justify-end space-x-3">
          <Link
            href={`/admin/orders/${id}`}
            className="px-4 py-2 border border-gray-200 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </Link>
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 rounded-md text-sm font-medium text-white hover:bg-blue-700"
          >
            Save Changes
          </button>
        </div>
      </form>

      {/* Product Selector Modal */}
      {showProductSelector && (
        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">Add Product</h3>
                  <button
                    type="button"
                    onClick={() => setShowProductSelector(false)}
                    className="text-gray-400 hover:text-gray-500"
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                </div>
                
                <div className="mb-4">
                  <input
                    type="text"
                    placeholder="Search products..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                
                <div className="max-h-64 overflow-y-auto">
                  <ul className="divide-y divide-gray-100">
                    {filteredProducts.map((product) => (
                      <li key={product.id} className="py-2">
                        <button
                          type="button"
                          onClick={() => handleAddProduct(product)}
                          className="w-full flex items-center px-2 py-2 hover:bg-gray-50 rounded-md"
                        >
                          <div className="h-10 w-10 flex-shrink-0 relative">
                            <Image
                              src={product.image}
                              alt={product.name}
                              fill
                              className="object-cover rounded-md"
                            />
                          </div>
                          <div className="ml-3 flex-1 flex justify-between items-center">
                            <div className="text-left">
                              <p className="text-sm font-medium text-gray-900">{product.name}</p>
                              <p className="text-xs text-gray-500">{product.sku}</p>
                            </div>
                            <p className="text-sm font-medium text-gray-900">${product.price.toFixed(2)}</p>
                          </div>
                        </button>
                      </li>
                    ))}
                    {filteredProducts.length === 0 && (
                      <li className="py-4 text-center text-sm text-gray-500">
                        No products found
                      </li>
                    )}
                  </ul>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  onClick={() => setShowProductSelector(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 