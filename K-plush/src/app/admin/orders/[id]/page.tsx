'use client';

import { useState, use } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { 
  ArrowLeftIcon, 
  PencilIcon, 
  PrinterIcon,
  DocumentDuplicateIcon,
  ChevronDownIcon,
  PaperClipIcon,
  ChatBubbleLeftIcon
} from '@heroicons/react/24/outline';
import InvoiceModal from '@/components/admin/InvoiceModal';

// Mock order data
const orderData = {
  id: 'ORD-001',
  date: '2024-01-15T10:30:00',
  status: 'processing',
  customer: {
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    address: {
      street: '123 Main St',
      city: 'New York',
      state: 'NY',
      zip: '10001',
      country: 'United States'
    }
  },
  payment: {
    method: 'Credit Card',
    cardLast4: '4242',
    status: 'paid',
    total: 299.99,
    subtotal: 279.99,
    tax: 20.00,
    shipping: 0,
    discount: 0
  },
  shipping: {
    method: 'Standard Shipping',
    trackingNumber: 'TRK123456789',
    carrier: 'UPS',
    estimatedDelivery: '2024-01-20'
  },
  items: [
    {
      id: 1,
      name: '<PERSON> (Large)',
      sku: 'TB-LRG-001',
      price: 149.99,
      quantity: 1,
      image: '/images/products/placeholder.jpg'
    },
    {
      id: 2,
      name: 'Plush Elephant',
      sku: 'PE-MED-002',
      price: 130.00,
      quantity: 1,
      image: '/images/products/placeholder.jpg'
    }
  ],
  history: [
    {
      date: '2024-01-15T10:30:00',
      status: 'pending',
      note: 'Order placed by customer'
    },
    {
      date: '2024-01-15T10:35:00',
      status: 'processing',
      note: 'Payment confirmed'
    }
  ],
  notes: [
    {
      date: '2024-01-15T11:00:00',
      user: 'Admin',
      content: 'Customer requested express shipping but selected standard at checkout. Keeping as standard per order.'
    }
  ]
};

// Status badge component
const StatusBadge = ({ status }: { status: string }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-amber-50 text-amber-700 border-amber-200';
      case 'processing':
        return 'bg-blue-50 text-blue-700 border-blue-200';
      case 'shipped':
        return 'bg-indigo-50 text-indigo-700 border-indigo-200';
      case 'delivered':
        return 'bg-green-50 text-green-700 border-green-200';
      case 'cancelled':
        return 'bg-red-50 text-red-700 border-red-200';
      case 'paid':
        return 'bg-green-50 text-green-700 border-green-200';
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };

  return (
    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-md border ${getStatusColor(status)}`}>
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  );
};

export default function OrderDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = use(params);
  const [activeTab, setActiveTab] = useState('details');
  const [showStatusDropdown, setShowStatusDropdown] = useState(false);
  const [currentStatus, setCurrentStatus] = useState(orderData.status);
  const [note, setNote] = useState('');
  const [showInvoiceModal, setShowInvoiceModal] = useState(false);

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  // Handle status change
  const handleStatusChange = (status: string) => {
    setCurrentStatus(status);
    setShowStatusDropdown(false);
    // Here you would also update the status in your database
  };

  // Add note
  const handleAddNote = () => {
    if (note.trim()) {
      // Here you would add the note to your database
      // For now, we'll just clear the input
      setNote('');
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center">
          <Link href="/admin/orders" className="mr-4 p-1 rounded-full hover:bg-gray-100">
            <ArrowLeftIcon className="h-5 w-5 text-gray-500" />
          </Link>
          <div>
            <div className="flex items-center gap-3">
              <h1 className="text-2xl font-medium text-gray-900">
                Order {id}
              </h1>
              <StatusBadge status={currentStatus} />
            </div>
            <p className="text-sm text-gray-500 mt-1">
              Placed on {formatDate(orderData.date)}
            </p>
          </div>
        </div>
        <div className="flex flex-wrap gap-3">
          <div className="relative">
            <button 
              onClick={() => setShowStatusDropdown(!showStatusDropdown)}
              className="flex items-center px-4 py-2 border border-gray-200 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Update Status
              <ChevronDownIcon className="ml-1.5 h-4 w-4" />
            </button>
            {showStatusDropdown && (
              <div className="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg border border-gray-100 z-10">
                <div className="py-1">
                  {['pending', 'processing', 'shipped', 'delivered', 'cancelled'].map((status) => (
                    <button
                      key={status}
                      onClick={() => handleStatusChange(status)}
                      className={`block w-full text-left px-4 py-2 text-sm ${currentStatus === status ? 'bg-gray-50 text-blue-600' : 'text-gray-700 hover:bg-gray-50'}`}
                    >
                      {status.charAt(0).toUpperCase() + status.slice(1)}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
          <Link 
            href={`/admin/orders/${id}/edit`}
            className="flex items-center px-4 py-2 border border-gray-200 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            <PencilIcon className="h-4 w-4 mr-1.5" />
            Edit
          </Link>
          <button className="flex items-center px-4 py-2 border border-gray-200 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
            <PrinterIcon className="h-4 w-4 mr-1.5" />
            Print
          </button>
          <button 
            onClick={() => setShowInvoiceModal(true)}
            className="flex items-center px-4 py-2 bg-blue-600 rounded-md text-sm font-medium text-white hover:bg-blue-700"
          >
            <DocumentDuplicateIcon className="h-4 w-4 mr-1.5" />
            Invoice
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('details')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'details'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Order Details
          </button>
          <button
            onClick={() => setActiveTab('history')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'history'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            History
          </button>
          <button
            onClick={() => setActiveTab('notes')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'notes'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Notes
          </button>
        </nav>
      </div>

      {/* Content based on active tab */}
      <div className="space-y-6">
        {activeTab === 'details' && (
          <>
            {/* Order Summary */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Customer Information */}
              <div className="bg-white rounded-lg border border-gray-100 p-4">
                <h3 className="text-sm font-medium text-gray-900 mb-3">Customer Information</h3>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm font-medium text-gray-900">{orderData.customer.name}</p>
                    <p className="text-sm text-gray-500">{orderData.customer.email}</p>
                    <p className="text-sm text-gray-500">{orderData.customer.phone}</p>
                  </div>
                  <div>
                    <p className="text-xs font-medium text-gray-500 mb-1">Shipping Address</p>
                    <p className="text-sm text-gray-700">{orderData.customer.address.street}</p>
                    <p className="text-sm text-gray-700">
                      {orderData.customer.address.city}, {orderData.customer.address.state} {orderData.customer.address.zip}
                    </p>
                    <p className="text-sm text-gray-700">{orderData.customer.address.country}</p>
                  </div>
                </div>
              </div>

              {/* Payment Information */}
              <div className="bg-white rounded-lg border border-gray-100 p-4">
                <h3 className="text-sm font-medium text-gray-900 mb-3">Payment Information</h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <p className="text-sm text-gray-500">Method</p>
                    <p className="text-sm text-gray-900">
                      {orderData.payment.method} (*{orderData.payment.cardLast4})
                    </p>
                  </div>
                  <div className="flex justify-between items-center">
                    <p className="text-sm text-gray-500">Status</p>
                    <StatusBadge status={orderData.payment.status} />
                  </div>
                  <div className="flex justify-between items-center">
                    <p className="text-sm text-gray-500">Subtotal</p>
                    <p className="text-sm text-gray-900">${orderData.payment.subtotal.toFixed(2)}</p>
                  </div>
                  <div className="flex justify-between items-center">
                    <p className="text-sm text-gray-500">Tax</p>
                    <p className="text-sm text-gray-900">${orderData.payment.tax.toFixed(2)}</p>
                  </div>
                  <div className="flex justify-between items-center">
                    <p className="text-sm text-gray-500">Shipping</p>
                    <p className="text-sm text-gray-900">${orderData.payment.shipping.toFixed(2)}</p>
                  </div>
                  {orderData.payment.discount > 0 && (
                    <div className="flex justify-between items-center">
                      <p className="text-sm text-gray-500">Discount</p>
                      <p className="text-sm text-red-600">-${orderData.payment.discount.toFixed(2)}</p>
                    </div>
                  )}
                  <div className="flex justify-between items-center pt-2 border-t border-gray-100">
                    <p className="text-sm font-medium text-gray-900">Total</p>
                    <p className="text-sm font-medium text-gray-900">${orderData.payment.total.toFixed(2)}</p>
                  </div>
                </div>
              </div>

              {/* Shipping Information */}
              <div className="bg-white rounded-lg border border-gray-100 p-4">
                <h3 className="text-sm font-medium text-gray-900 mb-3">Shipping Information</h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <p className="text-sm text-gray-500">Method</p>
                    <p className="text-sm text-gray-900">{orderData.shipping.method}</p>
                  </div>
                  <div className="flex justify-between items-center">
                    <p className="text-sm text-gray-500">Carrier</p>
                    <p className="text-sm text-gray-900">{orderData.shipping.carrier}</p>
                  </div>
                  <div className="flex justify-between items-center">
                    <p className="text-sm text-gray-500">Tracking</p>
                    <p className="text-sm text-blue-600 hover:underline cursor-pointer">
                      {orderData.shipping.trackingNumber}
                    </p>
                  </div>
                  <div className="flex justify-between items-center">
                    <p className="text-sm text-gray-500">Est. Delivery</p>
                    <p className="text-sm text-gray-900">
                      {new Date(orderData.shipping.estimatedDelivery).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Order Items */}
            <div className="bg-white rounded-lg border border-gray-100">
              <div className="px-6 py-4 border-b border-gray-100">
                <h3 className="text-base font-medium text-gray-900">Order Items</h3>
              </div>
              <div className="overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-100">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Product
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          SKU
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Price
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Quantity
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Total
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-100">
                      {orderData.items.map((item) => (
                        <tr key={item.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="h-10 w-10 flex-shrink-0 relative">
                                <Image
                                  src={item.image}
                                  alt={item.name}
                                  fill
                                  className="object-cover rounded-md"
                                />
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900">
                                  {item.name}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {item.sku}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                            ${item.price.toFixed(2)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                            {item.quantity}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-right">
                            ${(item.price * item.quantity).toFixed(2)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </>
        )}

        {activeTab === 'history' && (
          <div className="bg-white rounded-lg border border-gray-100">
            <div className="px-6 py-4 border-b border-gray-100">
              <h3 className="text-base font-medium text-gray-900">Order History</h3>
            </div>
            <div className="p-6">
              <div className="flow-root">
                <ul className="-mb-8">
                  {orderData.history.map((event, eventIdx) => (
                    <li key={eventIdx}>
                      <div className="relative pb-8">
                        {eventIdx !== orderData.history.length - 1 ? (
                          <span className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true" />
                        ) : null}
                        <div className="relative flex space-x-3">
                          <div>
                            <span className="h-8 w-8 rounded-full bg-blue-50 flex items-center justify-center">
                              <span className="h-2.5 w-2.5 rounded-full bg-blue-600" />
                            </span>
                          </div>
                          <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                            <div>
                              <p className="text-sm text-gray-500">
                                {event.note}{' '}
                                <span className="font-medium text-gray-900">
                                  <StatusBadge status={event.status} />
                                </span>
                              </p>
                            </div>
                            <div className="text-right text-sm whitespace-nowrap text-gray-500">
                              {formatDate(event.date)}
                            </div>
                          </div>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'notes' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg border border-gray-100">
              <div className="px-6 py-4 border-b border-gray-100">
                <h3 className="text-base font-medium text-gray-900">Order Notes</h3>
              </div>
              <div className="p-6">
                <div className="flow-root">
                  <ul className="-mb-8">
                    {orderData.notes.map((noteItem, noteIdx) => (
                      <li key={noteIdx}>
                        <div className="relative pb-8">
                          {noteIdx !== orderData.notes.length - 1 ? (
                            <span className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true" />
                          ) : null}
                          <div className="relative flex space-x-3">
                            <div>
                              <span className="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center">
                                <ChatBubbleLeftIcon className="h-4 w-4 text-gray-500" />
                              </span>
                            </div>
                            <div className="min-w-0 flex-1">
                              <div className="text-sm text-gray-500">
                                <span className="font-medium text-gray-900">{noteItem.user}</span> added a note
                              </div>
                              <div className="mt-2 text-sm text-gray-700">
                                <p>{noteItem.content}</p>
                              </div>
                              <div className="mt-1 text-xs text-gray-500">
                                {formatDate(noteItem.date)}
                              </div>
                            </div>
                          </div>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>

            {/* Add Note Form */}
            <div className="bg-white rounded-lg border border-gray-100 p-4">
              <h3 className="text-sm font-medium text-gray-900 mb-3">Add Note</h3>
              <div className="space-y-3">
                <textarea
                  value={note}
                  onChange={(e) => setNote(e.target.value)}
                  rows={3}
                  className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Add a note about this order..."
                />
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <button className="flex items-center text-sm text-gray-500 hover:text-gray-700">
                      <PaperClipIcon className="h-4 w-4 mr-1" />
                      Attach File
                    </button>
                  </div>
                  <button
                    onClick={handleAddNote}
                    disabled={!note.trim()}
                    className={`px-4 py-2 rounded-md text-sm font-medium ${
                      note.trim()
                        ? 'bg-blue-600 text-white hover:bg-blue-700'
                        : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    }`}
                  >
                    Add Note
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Invoice Modal */}
      <InvoiceModal
        isOpen={showInvoiceModal}
        onClose={() => setShowInvoiceModal(false)}
        orderId={orderData.id}
        orderDate={orderData.date}
        customer={orderData.customer}
        items={orderData.items}
        payment={orderData.payment}
      />
    </div>
  );
} 