'use client';

import { useState, use } from 'react';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import ProductForm, { Product } from '@/components/admin/ProductForm';

// This would normally come from a database
const mockProduct = {
  id: '1',
  name: 'Modern Sofa',
  sku: 'MS-001',
  category: 'living-room',
  price: 1299.99,
  stock: 25,
  status: 'in-stock',
  description: 'A comfortable modern sofa with clean lines and durable fabric. Perfect for contemporary living spaces.',
  images: ['/images/products/placeholder.jpg'],
  tags: ['modern', 'comfortable', 'durable'],
  specifications: [
    { name: 'Dimensions', value: '84" W x 36" D x 32" H' },
    { name: 'Material', value: 'Premium fabric' },
    { name: 'Weight', value: '85 lbs' }
  ]
};

export default function EditProductPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const { id } = use(params);
  
  // Initialize form state with existing product data
  const [formData] = useState({
    id: mockProduct.id,
    name: mockProduct.name,
    sku: mockProduct.sku,
    description: mockProduct.description,
    price: mockProduct.price,
    category: mockProduct.category,
    stock: mockProduct.stock,
    status: mockProduct.status,
    images: mockProduct.images
  });

  const handleUpdateProduct = (data: Product) => {
    // In a real app, this would be an API call to update the product
    console.log('Updating product:', data);
    
    // Redirect to the product detail page after update
    router.push(`/admin/products/${id}`);
  };

  return (
    <div className="space-y-4">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center space-x-2">
            <Link href={`/admin/products/${id}`} className="text-gray-500 hover:text-blue-600">
              <ArrowLeftIcon className="h-5 w-5" />
            </Link>
            <h1 className="text-2xl font-medium text-gray-900">
              Edit Product
            </h1>
          </div>
          <p className="text-sm text-gray-500 mt-1">
            Edit product details for {mockProduct.name}
          </p>
        </div>
      </div>

      {/* Product Form */}
      <div className="bg-white rounded-lg border border-gray-100">
        <div className="px-6 py-4 border-b border-gray-100">
          <h3 className="text-base font-medium text-gray-900">
            Product Information
          </h3>
        </div>
        <div className="p-6">
          <ProductForm 
            initialData={formData}
            isEditing={true}
            onSubmit={handleUpdateProduct}
            cancelHref={`/admin/products/${id}`}
          />
        </div>
      </div>
    </div>
  );
} 