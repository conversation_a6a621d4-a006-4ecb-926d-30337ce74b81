'use client';

import { use } from 'react';
import { ArrowLeftIcon, PencilIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';

// This would normally come from a database
const mockProduct = {
  id: '1',
  name: 'Modern Sofa',
  sku: 'MS-001',
  category: 'Living Room',
  price: 1299.99,
  stock: 25,
  status: 'In Stock',
  description: 'A comfortable modern sofa with clean lines and durable fabric. Perfect for contemporary living spaces.',
  images: ['/images/products/placeholder.jpg'],
  createdAt: '2024-01-10T12:00:00Z',
  updatedAt: '2024-01-15T14:30:00Z',
};

export default function ProductDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = use(params);
  // In a real app, we would fetch the product data based on the ID
  const product = mockProduct;

  return (
    <div className="space-y-4">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center space-x-2">
            <Link href="/admin/products" className="text-gray-500 hover:text-blue-600">
              <ArrowLeftIcon className="h-5 w-5" />
            </Link>
            <h1 className="text-2xl font-medium text-gray-900">
              {product.name}
            </h1>
          </div>
          <p className="text-sm text-gray-500 mt-1">
            SKU: {product.sku}
          </p>
        </div>
        <div className="flex space-x-3">
          <Link
            href={`/admin/products/${id}/edit`}
            className="flex items-center px-4 py-2 border border-gray-200 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            <PencilIcon className="h-4 w-4 mr-1.5" />
            Edit Product
          </Link>
          <button
            className="px-4 py-2 bg-blue-600 rounded-md text-sm font-medium text-white hover:bg-blue-700"
          >
            Publish
          </button>
        </div>
      </div>

      {/* Product Details */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        {/* Main Product Info */}
        <div className="lg:col-span-2 space-y-4">
          {/* Product Images */}
          <div className="bg-white rounded-lg border border-gray-100">
            <div className="px-6 py-4 border-b border-gray-100">
              <h3 className="text-base font-medium text-gray-900">
                Product Images
              </h3>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="aspect-square bg-gray-100 rounded-md flex items-center justify-center">
                  <span className="text-gray-400">No image</span>
                </div>
              </div>
            </div>
          </div>

          {/* Product Description */}
          <div className="bg-white rounded-lg border border-gray-100">
            <div className="px-6 py-4 border-b border-gray-100">
              <h3 className="text-base font-medium text-gray-900">
                Description
              </h3>
            </div>
            <div className="p-6">
              <p className="text-gray-700 text-sm">
                {product.description}
              </p>
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-4">
          {/* Product Information */}
          <div className="bg-white rounded-lg border border-gray-100">
            <div className="px-6 py-4 border-b border-gray-100">
              <h3 className="text-base font-medium text-gray-900">
                Product Information
              </h3>
            </div>
            <div className="px-6 py-4">
              <dl className="space-y-3 text-sm">
                <div className="grid grid-cols-3 gap-4">
                  <dt className="text-gray-500">Price</dt>
                  <dd className="text-gray-900 col-span-2">${product.price.toFixed(2)}</dd>
                </div>
                <div className="grid grid-cols-3 gap-4">
                  <dt className="text-gray-500">Category</dt>
                  <dd className="text-gray-900 col-span-2">{product.category}</dd>
                </div>
                <div className="grid grid-cols-3 gap-4">
                  <dt className="text-gray-500">Stock</dt>
                  <dd className="text-gray-900 col-span-2">
                    <span className="inline-flex px-2 py-1 text-xs font-medium rounded-md bg-green-50 text-green-700 border border-green-200">
                      {product.status} ({product.stock})
                    </span>
                  </dd>
                </div>
              </dl>
            </div>
          </div>

          {/* Metadata */}
          <div className="bg-white rounded-lg border border-gray-100">
            <div className="px-6 py-4 border-b border-gray-100">
              <h3 className="text-base font-medium text-gray-900">
                Metadata
              </h3>
            </div>
            <div className="px-6 py-4">
              <dl className="space-y-3 text-sm">
                <div className="grid grid-cols-3 gap-4">
                  <dt className="text-gray-500">Created</dt>
                  <dd className="text-gray-900 col-span-2">
                    {new Date(product.createdAt).toLocaleDateString()}
                  </dd>
                </div>
                <div className="grid grid-cols-3 gap-4">
                  <dt className="text-gray-500">Updated</dt>
                  <dd className="text-gray-900 col-span-2">
                    {new Date(product.updatedAt).toLocaleDateString()}
                  </dd>
                </div>
                <div className="grid grid-cols-3 gap-4">
                  <dt className="text-gray-500">ID</dt>
                  <dd className="text-gray-900 col-span-2">{product.id}</dd>
                </div>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 