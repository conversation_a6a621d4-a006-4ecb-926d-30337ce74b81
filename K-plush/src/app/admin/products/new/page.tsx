'use client';

import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import ProductForm, { Product } from '@/components/admin/ProductForm';

export default function NewProductPage() {
  const router = useRouter();

  const handleCreateProduct = (data: Product) => {
    // In a real app, this would be an API call to create the product
    console.log('Creating product:', data);
    
    // Redirect to the products list after creation
    router.push('/admin/products');
  };

  return (
    <div className="space-y-4">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center space-x-2">
            <Link href="/admin/products" className="text-gray-500 hover:text-blue-600">
              <ArrowLeftIcon className="h-5 w-5" />
            </Link>
            <h1 className="text-2xl font-medium text-gray-900">
              Add New Product
            </h1>
          </div>
          <p className="text-sm text-gray-500 mt-1">
            Create a new product in your catalog
          </p>
        </div>
      </div>

      {/* Product Form */}
      <div className="bg-white rounded-lg border border-gray-100">
        <div className="px-6 py-4 border-b border-gray-100">
          <h3 className="text-base font-medium text-gray-900">
            Product Information
          </h3>
        </div>
        <div className="p-6">
          <ProductForm 
            onSubmit={handleCreateProduct}
            cancelHref="/admin/products"
          />
        </div>
      </div>
    </div>
  );
} 