import Link from 'next/link';
import { PlusIcon } from '@heroicons/react/24/outline';

// Mock data for products
const products = [
  {
    id: '1',
    name: 'Modern Sofa',
    sku: 'MS-001',
    category: 'Living Room',
    price: 1299.99,
    stock: 25,
    status: 'In Stock',
  },
  {
    id: '2',
    name: 'Teddy Bear',
    sku: 'TB-001',
    category: 'Plush Toys',
    price: 24.99,
    stock: 50,
    status: 'In Stock',
  },
  {
    id: '3',
    name: 'Unicorn Plush',
    sku: 'UP-001',
    category: 'Stuffed Animals',
    price: 34.99,
    stock: 0,
    status: 'Out of Stock',
  },
  {
    id: '4',
    name: 'Custom Elephant',
    sku: 'CE-001',
    category: 'Custom Plush',
    price: 49.99,
    stock: 10,
    status: 'In Stock',
  },
  {
    id: '5',
    name: 'Plush Keychain',
    sku: 'PK-001',
    category: 'Accessories',
    price: 9.99,
    stock: 100,
    status: 'In Stock',
  }
];

const getStatusColor = (status: string) => {
  switch (status) {
    case 'In Stock':
      return 'bg-green-50 text-green-700 border-green-200';
    case 'Out of Stock':
      return 'bg-red-50 text-red-700 border-red-200';
    case 'Backorder':
      return 'bg-amber-50 text-amber-700 border-amber-200';
    default:
      return 'bg-gray-50 text-gray-700 border-gray-200';
  }
};

export default function AdminProductsPage() {
  return (
    <div className="space-y-4">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-medium text-gray-900">
            Products
          </h1>
          <p className="text-sm text-gray-500 mt-1">
            Manage your product catalog
          </p>
        </div>
        <Link 
          href="/admin/products/new"
          className="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
        >
          <PlusIcon className="h-4 w-4 mr-1.5" />
          Add Product
        </Link>
      </div>

      {/* Products Table */}
      <div className="bg-white rounded-lg border border-gray-100">
        <div className="px-6 py-4 border-b border-gray-100">
          <h3 className="text-base font-medium text-gray-900">
            All Products
          </h3>
        </div>
        <div className="overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-100">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Product
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Category
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Price
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Stock
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-100">
                {products.map((product) => (
                  <tr key={product.id} className="hover:bg-gray-50">
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-10 w-10 flex-shrink-0 bg-gray-100 rounded-md"></div>
                        <div className="ml-3">
                          <div className="text-sm font-medium text-gray-900">
                            <Link href={`/admin/products/${product.id}`} className="hover:text-blue-600">
                              {product.name}
                            </Link>
                          </div>
                          <div className="text-xs text-gray-500">
                            SKU: {product.sku}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{product.category}</div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div className="text-sm text-gray-900">${product.price.toFixed(2)}</div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded-md border ${getStatusColor(product.status)}`}>
                        {product.status} {product.stock > 0 && `(${product.stock})`}
                      </span>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-3">
                        <Link href={`/admin/products/${product.id}`} className="text-gray-500 hover:text-blue-600">
                          View
                        </Link>
                        <Link href={`/admin/products/${product.id}/edit`} className="text-gray-500 hover:text-blue-600">
                          Edit
                        </Link>
                        <button className="text-gray-500 hover:text-red-600">
                          Delete
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
} 