'use client';

import { useState } from 'react';
import { 
  Cog6ToothIcon,
  GlobeAltIcon,
  ShieldCheckIcon,
  BellIcon,
  EnvelopeIcon,
  PhotoIcon,
  CurrencyDollarIcon,
  DocumentTextIcon,
  UserIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';

// Settings navigation items
const settingsNavigation = [
  { id: 'general', name: 'General', icon: Cog6ToothIcon },
  { id: 'appearance', name: 'Appearance', icon: PhotoIcon },
  { id: 'security', name: 'Security', icon: ShieldCheckIcon },
  { id: 'localization', name: 'Localization', icon: GlobeAltIcon },
  { id: 'notifications', name: 'Notifications', icon: BellIcon },
  { id: 'email', name: 'Email', icon: EnvelopeIcon },
  { id: 'payments', name: 'Payment Settings', icon: CurrencyDollarIcon },
  { id: 'legal', name: 'Legal', icon: DocumentTextIcon },
  { id: 'users', name: 'User Settings', icon: UserIcon },
  { id: 'system', name: 'System', icon: ArrowPathIcon },
];

export default function AdminSettingsPage() {
  const [activeSection, setActiveSection] = useState('general');
  const [formChanged, setFormChanged] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  
  // Handle form changes
  const handleFormChange = () => {
    setFormChanged(true);
    setSaveSuccess(false);
  };
  
  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, you would save the settings to your backend
    console.log('Settings saved');
    setFormChanged(false);
    setSaveSuccess(true);
    
    // Hide success message after 3 seconds
    setTimeout(() => {
      setSaveSuccess(false);
    }, 3000);
  };

  return (
    <div className="flex flex-col lg:flex-row gap-6">
      {/* Settings Navigation */}
      <div className="w-full lg:w-64 flex-shrink-0">
        <div className="bg-white rounded-lg border border-gray-100 overflow-hidden">
          <div className="px-4 py-5 border-b border-gray-100">
            <h3 className="text-base font-medium text-gray-900">Settings</h3>
            <p className="mt-1 text-sm text-gray-500">
              Manage your store configuration
            </p>
          </div>
          <nav className="py-2">
            {settingsNavigation.map((item) => (
              <button
                key={item.id}
                onClick={() => setActiveSection(item.id)}
                className={`w-full flex items-center px-4 py-2.5 text-sm font-medium ${
                  activeSection === item.id
                    ? 'bg-blue-50 text-blue-700'
                    : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                }`}
              >
                <item.icon
                  className={`w-5 h-5 mr-3 ${
                    activeSection === item.id ? 'text-blue-600' : 'text-gray-400'
                  }`}
                />
                {item.name}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Settings Content */}
      <div className="flex-1">
        <div className="bg-white rounded-lg border border-gray-100">
          <form onSubmit={handleSubmit}>
            {/* Form Header */}
            <div className="px-6 py-4 border-b border-gray-100 flex items-center justify-between">
              <h3 className="text-base font-medium text-gray-900">
                {settingsNavigation.find(item => item.id === activeSection)?.name} Settings
              </h3>
              <div className="flex items-center space-x-3">
                {formChanged && (
                  <span className="text-sm text-amber-600">
                    Unsaved changes
                  </span>
                )}
                {saveSuccess && (
                  <span className="text-sm text-green-600">
                    Settings saved successfully
                  </span>
                )}
                <button
                  type="submit"
                  disabled={!formChanged}
                  className={`px-4 py-2 rounded-md text-sm font-medium ${
                    formChanged
                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                      : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  }`}
                >
                  Save Changes
                </button>
              </div>
            </div>

            {/* Form Content based on active section */}
            <div className="p-6">
              {/* General Settings */}
              {activeSection === 'general' && (
                <div className="space-y-6">
                  <div>
                    <label htmlFor="site-name" className="block text-sm font-medium text-gray-700 mb-1">
                      Site Name
                    </label>
                    <input
                      id="site-name"
                      type="text"
                      defaultValue="K-Plush"
                      onChange={handleFormChange}
                      className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      The name of your store as it appears throughout the site
                    </p>
                  </div>
                  
                  <div>
                    <label htmlFor="site-description" className="block text-sm font-medium text-gray-700 mb-1">
                      Site Description
                    </label>
                    <textarea
                      id="site-description"
                      rows={3}
                      defaultValue="Premium handcrafted plush toys for all ages"
                      onChange={handleFormChange}
                      className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Brief description of your store used for SEO and social sharing
                    </p>
                  </div>
                  
                  <div>
                    <label htmlFor="contact-email" className="block text-sm font-medium text-gray-700 mb-1">
                      Contact Email
                    </label>
                    <input
                      id="contact-email"
                      type="email"
                      defaultValue="<EMAIL>"
                      onChange={handleFormChange}
                      className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Primary contact email displayed on the site
                    </p>
                  </div>
                  
                  <div>
                    <label htmlFor="phone-number" className="block text-sm font-medium text-gray-700 mb-1">
                      Phone Number
                    </label>
                    <input
                      id="phone-number"
                      type="text"
                      defaultValue="+****************"
                      onChange={handleFormChange}
                      className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
                      Business Address
                    </label>
                    <textarea
                      id="address"
                      rows={3}
                      defaultValue="123 Plush Lane, Toyville, NY 10001, United States"
                      onChange={handleFormChange}
                      className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Default Currency
                    </label>
                    <select
                      defaultValue="USD"
                      onChange={handleFormChange}
                      className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="USD">US Dollar (USD)</option>
                      <option value="EUR">Euro (EUR)</option>
                      <option value="GBP">British Pound (GBP)</option>
                      <option value="CAD">Canadian Dollar (CAD)</option>
                      <option value="AUD">Australian Dollar (AUD)</option>
                      <option value="JPY">Japanese Yen (JPY)</option>
                    </select>
                  </div>
                </div>
              )}

              {/* Appearance Settings */}
              {activeSection === 'appearance' && (
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Site Logo
                    </label>
                    <div className="flex items-center space-x-4">
                      <div className="w-16 h-16 bg-blue-100 rounded-md flex items-center justify-center">
                        <span className="text-blue-600 font-bold text-xl">K</span>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          type="button"
                          onClick={handleFormChange}
                          className="px-3 py-1.5 border border-gray-200 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                        >
                          Change Logo
                        </button>
                        <button
                          type="button"
                          onClick={handleFormChange}
                          className="px-3 py-1.5 border border-gray-200 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                        >
                          Remove
                        </button>
                      </div>
                    </div>
                    <p className="mt-2 text-xs text-gray-500">
                      Recommended size: 200x200px. PNG or SVG format.
                    </p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Favicon
                    </label>
                    <div className="flex items-center space-x-4">
                      <div className="w-8 h-8 bg-blue-600 rounded-sm flex items-center justify-center">
                        <span className="text-white font-bold text-xs">K</span>
                      </div>
                      <button
                        type="button"
                        onClick={handleFormChange}
                        className="px-3 py-1.5 border border-gray-200 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                      >
                        Change Favicon
                      </button>
                    </div>
                    <p className="mt-2 text-xs text-gray-500">
                      Recommended size: 32x32px. PNG or ICO format.
                    </p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Theme Color
                    </label>
                    <div className="flex items-center space-x-4">
                      <input
                        type="color"
                        defaultValue="#2563eb"
                        onChange={handleFormChange}
                        className="h-9 w-16 p-0 border-0"
                      />
                      <input
                        type="text"
                        defaultValue="#2563eb"
                        onChange={handleFormChange}
                        className="w-28 px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      Primary color used throughout the site
                    </p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Font Family
                    </label>
                    <select
                      defaultValue="inter"
                      onChange={handleFormChange}
                      className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="inter">Inter</option>
                      <option value="roboto">Roboto</option>
                      <option value="opensans">Open Sans</option>
                      <option value="lato">Lato</option>
                      <option value="poppins">Poppins</option>
                    </select>
                  </div>
                  
                  <div>
                    <div className="flex items-center">
                      <input
                        id="enable-dark-mode"
                        type="checkbox"
                        onChange={handleFormChange}
                        className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      />
                      <label htmlFor="enable-dark-mode" className="ml-2 block text-sm text-gray-700">
                        Enable dark mode toggle
                      </label>
                    </div>
                  </div>
                </div>
              )}

              {/* Security Settings */}
              {activeSection === 'security' && (
                <div className="space-y-6">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-3">Authentication</h4>
                    <div className="space-y-3">
                      <div className="flex items-center">
                        <input
                          id="require-2fa"
                          type="checkbox"
                          onChange={handleFormChange}
                          className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        />
                        <label htmlFor="require-2fa" className="ml-2 block text-sm text-gray-700">
                          Require two-factor authentication for admin users
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input
                          id="password-strength"
                          type="checkbox"
                          defaultChecked
                          onChange={handleFormChange}
                          className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        />
                        <label htmlFor="password-strength" className="ml-2 block text-sm text-gray-700">
                          Enforce strong password policy
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input
                          id="session-timeout"
                          type="checkbox"
                          defaultChecked
                          onChange={handleFormChange}
                          className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        />
                        <label htmlFor="session-timeout" className="ml-2 block text-sm text-gray-700">
                          Enable session timeout
                        </label>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="session-length" className="block text-sm font-medium text-gray-700 mb-1">
                      Session Timeout (minutes)
                    </label>
                    <input
                      id="session-length"
                      type="number"
                      defaultValue="60"
                      min="5"
                      max="1440"
                      onChange={handleFormChange}
                      className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Time in minutes before an inactive session is logged out
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-3">API Security</h4>
                    <div className="space-y-3">
                      <div className="flex items-center">
                        <input
                          id="rate-limiting"
                          type="checkbox"
                          defaultChecked
                          onChange={handleFormChange}
                          className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        />
                        <label htmlFor="rate-limiting" className="ml-2 block text-sm text-gray-700">
                          Enable API rate limiting
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input
                          id="cors"
                          type="checkbox"
                          defaultChecked
                          onChange={handleFormChange}
                          className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        />
                        <label htmlFor="cors" className="ml-2 block text-sm text-gray-700">
                          Restrict CORS to approved domains
                        </label>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="approved-domains" className="block text-sm font-medium text-gray-700 mb-1">
                      Approved Domains
                    </label>
                    <textarea
                      id="approved-domains"
                      rows={3}
                      defaultValue="kplush.com&#10;admin.kplush.com"
                      onChange={handleFormChange}
                      className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="One domain per line"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      List domains that are allowed to access your API (one per line)
                    </p>
                  </div>
                </div>
              )}

              {/* Localization Settings */}
              {activeSection === 'localization' && (
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Default Language
                    </label>
                    <select
                      defaultValue="en-US"
                      onChange={handleFormChange}
                      className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="en-US">English (United States)</option>
                      <option value="en-GB">English (United Kingdom)</option>
                      <option value="es">Spanish</option>
                      <option value="fr">French</option>
                      <option value="de">German</option>
                      <option value="ja">Japanese</option>
                      <option value="zh-CN">Chinese (Simplified)</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Timezone
                    </label>
                    <select
                      defaultValue="America/New_York"
                      onChange={handleFormChange}
                      className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="America/New_York">Eastern Time (US & Canada)</option>
                      <option value="America/Chicago">Central Time (US & Canada)</option>
                      <option value="America/Denver">Mountain Time (US & Canada)</option>
                      <option value="America/Los_Angeles">Pacific Time (US & Canada)</option>
                      <option value="Europe/London">London</option>
                      <option value="Europe/Paris">Paris</option>
                      <option value="Asia/Tokyo">Tokyo</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Date Format
                    </label>
                    <select
                      defaultValue="MM/DD/YYYY"
                      onChange={handleFormChange}
                      className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                      <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                      <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                      <option value="MMM D, YYYY">MMM D, YYYY</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Time Format
                    </label>
                    <select
                      defaultValue="12h"
                      onChange={handleFormChange}
                      className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="12h">12-hour (1:30 PM)</option>
                      <option value="24h">24-hour (13:30)</option>
                    </select>
                  </div>
                  
                  <div>
                    <div className="flex items-center">
                      <input
                        id="enable-multilingual"
                        type="checkbox"
                        onChange={handleFormChange}
                        className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      />
                      <label htmlFor="enable-multilingual" className="ml-2 block text-sm text-gray-700">
                        Enable multilingual support
                      </label>
                    </div>
                    <p className="mt-1 text-xs text-gray-500 pl-6">
                      Allow customers to view the store in different languages
                    </p>
                  </div>
                </div>
              )}
              
              {/* Other sections would be implemented similarly */}
              {(activeSection !== 'general' && 
                activeSection !== 'appearance' && 
                activeSection !== 'security' && 
                activeSection !== 'localization') && (
                <div className="py-8 text-center">
                  <h4 className="text-gray-500 text-sm font-medium">
                    {settingsNavigation.find(item => item.id === activeSection)?.name} settings will be implemented soon
                  </h4>
                  <p className="mt-2 text-gray-400 text-xs">
                    This section is under development
                  </p>
                </div>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
  );
} 