'use client';

import { useState } from 'react';
import { 
  MagnifyingGlassIcon, 
  PencilIcon,
  TrashIcon,
  PlusIcon,
  GlobeAmericasIcon,
  TruckIcon
} from '@heroicons/react/24/outline';

// Mock data for shipping zones
const shippingZones = [
  {
    id: 1,
    name: 'United States',
    regions: ['US'],
    methods: [
      {
        id: 1,
        name: 'Standard Shipping',
        cost: 5.99,
        freeAbove: 50,
        estimatedDays: '3-5'
      },
      {
        id: 2,
        name: 'Express Shipping',
        cost: 15.99,
        freeAbove: 100,
        estimatedDays: '1-2'
      }
    ]
  },
  {
    id: 2,
    name: 'Canada',
    regions: ['CA'],
    methods: [
      {
        id: 3,
        name: 'Standard International',
        cost: 12.99,
        freeAbove: 75,
        estimatedDays: '7-10'
      }
    ]
  },
  {
    id: 3,
    name: 'Europe',
    regions: ['EU', 'GB'],
    methods: [
      {
        id: 4,
        name: 'International Shipping',
        cost: 19.99,
        freeAbove: 100,
        estimatedDays: '10-14'
      }
    ]
  },
  {
    id: 4,
    name: 'Rest of World',
    regions: ['Worldwide'],
    methods: [
      {
        id: 5,
        name: 'International Economy',
        cost: 24.99,
        freeAbove: 150,
        estimatedDays: '14-21'
      }
    ]
  }
];

// Mock data for shipping carriers
const shippingCarriers = [
  {
    id: 1,
    name: 'USPS',
    status: 'active',
    trackingUrl: 'https://tools.usps.com/go/TrackConfirmAction_input',
    apiIntegrated: true
  },
  {
    id: 2,
    name: 'FedEx',
    status: 'active',
    trackingUrl: 'https://www.fedex.com/apps/fedextrack/',
    apiIntegrated: true
  },
  {
    id: 3,
    name: 'UPS',
    status: 'active',
    trackingUrl: 'https://www.ups.com/track',
    apiIntegrated: true
  },
  {
    id: 4,
    name: 'DHL',
    status: 'inactive',
    trackingUrl: 'https://www.dhl.com/en/express/tracking.html',
    apiIntegrated: false
  }
];

export default function AdminShippingPage() {
  const [activeTab, setActiveTab] = useState<'zones' | 'carriers' | 'settings'>('zones');
  const [searchQuery, setSearchQuery] = useState('');

  // Filter shipping zones or carriers based on search query
  const filteredZones = shippingZones.filter(zone => 
    zone.name.toLowerCase().includes(searchQuery.toLowerCase())
  );
  
  const filteredCarriers = shippingCarriers.filter(carrier => 
    carrier.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-medium text-gray-900">
            Shipping
          </h1>
          <p className="text-sm text-gray-500 mt-1">
            Manage shipping zones, methods, and carriers
          </p>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('zones')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'zones'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Shipping Zones
          </button>
          <button
            onClick={() => setActiveTab('carriers')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'carriers'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Carriers
          </button>
          <button
            onClick={() => setActiveTab('settings')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'settings'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Settings
          </button>
        </nav>
      </div>

      {/* Content based on active tab */}
      {activeTab === 'zones' && (
        <>
          {/* Search and Add */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="relative flex-1 max-w-md">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search shipping zones..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <button
              className="flex items-center px-4 py-2 bg-blue-600 rounded-md text-sm font-medium text-white hover:bg-blue-700"
            >
              <PlusIcon className="h-4 w-4 mr-1.5" />
              Add Shipping Zone
            </button>
          </div>

          {/* Shipping Zones */}
          <div className="space-y-6">
            {filteredZones.map((zone) => (
              <div key={zone.id} className="bg-white rounded-lg border border-gray-100">
                <div className="px-6 py-4 border-b border-gray-100 flex items-center justify-between">
                  <div className="flex items-center">
                    <GlobeAmericasIcon className="h-5 w-5 text-gray-400 mr-2" />
                    <h3 className="text-base font-medium text-gray-900">
                      {zone.name}
                    </h3>
                    <span className="ml-2 text-xs text-gray-500">
                      ({zone.regions.join(', ')})
                    </span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <button 
                      className="p-1 text-gray-500 hover:text-blue-600"
                    >
                      <PencilIcon className="h-4 w-4" />
                    </button>
                    <button className="p-1 text-gray-500 hover:text-red-600">
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
                <div className="p-6">
                  <h4 className="text-sm font-medium text-gray-700 mb-3">Shipping Methods</h4>
                  <div className="space-y-4">
                    {zone.methods.map((method) => (
                      <div key={method.id} className="flex items-center justify-between p-3 border border-gray-100 rounded-md">
                        <div>
                          <div className="flex items-center">
                            <TruckIcon className="h-4 w-4 text-gray-500 mr-2" />
                            <span className="text-sm font-medium text-gray-900">{method.name}</span>
                          </div>
                          <div className="mt-1 text-xs text-gray-500">
                            Estimated delivery: {method.estimatedDays} business days
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium text-gray-900">
                            ${method.cost.toFixed(2)}
                          </div>
                          {method.freeAbove > 0 && (
                            <div className="text-xs text-green-600">
                              Free above ${method.freeAbove.toFixed(2)}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                    
                    <button
                      className="flex items-center px-4 py-2 border border-gray-200 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                    >
                      <PlusIcon className="h-4 w-4 mr-1.5" />
                      Add Shipping Method
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </>
      )}

      {activeTab === 'carriers' && (
        <>
          {/* Search and Add */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="relative flex-1 max-w-md">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search carriers..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <button
              className="flex items-center px-4 py-2 bg-blue-600 rounded-md text-sm font-medium text-white hover:bg-blue-700"
            >
              <PlusIcon className="h-4 w-4 mr-1.5" />
              Add Carrier
            </button>
          </div>

          {/* Carriers Table */}
          <div className="bg-white rounded-lg border border-gray-100">
            <div className="px-6 py-4 border-b border-gray-100">
              <h3 className="text-base font-medium text-gray-900">
                Shipping Carriers
              </h3>
            </div>
            <div className="overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-100">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Carrier
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Tracking URL
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        API Integration
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-100">
                    {filteredCarriers.map((carrier) => (
                      <tr key={carrier.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="text-sm font-medium text-gray-900">
                              {carrier.name}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-md border ${
                            carrier.status === 'active' 
                              ? 'bg-green-50 text-green-700 border-green-200' 
                              : 'bg-gray-50 text-gray-700 border-gray-200'
                          }`}>
                            {carrier.status.charAt(0).toUpperCase() + carrier.status.slice(1)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <a 
                            href={carrier.trackingUrl} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:underline"
                          >
                            {carrier.trackingUrl}
                          </a>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-md border ${
                            carrier.apiIntegrated 
                              ? 'bg-green-50 text-green-700 border-green-200' 
                              : 'bg-red-50 text-red-700 border-red-200'
                          }`}>
                            {carrier.apiIntegrated ? 'Integrated' : 'Not Integrated'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-right">
                          <div className="flex justify-end space-x-3">
                            <button className="text-gray-500 hover:text-blue-600">
                              <PencilIcon className="h-4 w-4" />
                            </button>
                            <button className="text-gray-500 hover:text-red-600">
                              <TrashIcon className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </>
      )}

      {activeTab === 'settings' && (
        <div className="bg-white rounded-lg border border-gray-100 p-6">
          <h3 className="text-base font-medium text-gray-900 mb-4">
            Shipping Settings
          </h3>
          
          <div className="space-y-6">
            {/* Dimension Units */}
            <div>
              <label htmlFor="dimension-unit" className="block text-sm font-medium text-gray-700 mb-1">
                Dimension Unit
              </label>
              <select
                id="dimension-unit"
                className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                defaultValue="in"
              >
                <option value="in">Inches (in)</option>
                <option value="cm">Centimeters (cm)</option>
                <option value="mm">Millimeters (mm)</option>
              </select>
              <p className="mt-1 text-xs text-gray-500">
                Used for product dimensions and package sizes
              </p>
            </div>
            
            {/* Weight Units */}
            <div>
              <label htmlFor="weight-unit" className="block text-sm font-medium text-gray-700 mb-1">
                Weight Unit
              </label>
              <select
                id="weight-unit"
                className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                defaultValue="oz"
              >
                <option value="oz">Ounces (oz)</option>
                <option value="lb">Pounds (lb)</option>
                <option value="kg">Kilograms (kg)</option>
                <option value="g">Grams (g)</option>
              </select>
              <p className="mt-1 text-xs text-gray-500">
                Used for product weight and shipping calculations
              </p>
            </div>
            
            {/* Shipping Origin */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">Shipping Origin</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="origin-address" className="block text-xs font-medium text-gray-700 mb-1">
                    Address
                  </label>
                  <input
                    id="origin-address"
                    type="text"
                    className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    defaultValue="123 Shipping St"
                  />
                </div>
                <div>
                  <label htmlFor="origin-city" className="block text-xs font-medium text-gray-700 mb-1">
                    City
                  </label>
                  <input
                    id="origin-city"
                    type="text"
                    className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    defaultValue="New York"
                  />
                </div>
                <div>
                  <label htmlFor="origin-state" className="block text-xs font-medium text-gray-700 mb-1">
                    State/Province
                  </label>
                  <input
                    id="origin-state"
                    type="text"
                    className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    defaultValue="NY"
                  />
                </div>
                <div>
                  <label htmlFor="origin-zip" className="block text-xs font-medium text-gray-700 mb-1">
                    ZIP/Postal Code
                  </label>
                  <input
                    id="origin-zip"
                    type="text"
                    className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    defaultValue="10001"
                  />
                </div>
                <div>
                  <label htmlFor="origin-country" className="block text-xs font-medium text-gray-700 mb-1">
                    Country
                  </label>
                  <select
                    id="origin-country"
                    className="block w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    defaultValue="US"
                  >
                    <option value="US">United States</option>
                    <option value="CA">Canada</option>
                    <option value="GB">United Kingdom</option>
                    <option value="AU">Australia</option>
                  </select>
                </div>
              </div>
            </div>
            
            {/* Options */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">Shipping Options</h4>
              <div className="space-y-3">
                <div className="flex items-center">
                  <input
                    id="enable-shipping-calc"
                    type="checkbox"
                    checked
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <label htmlFor="enable-shipping-calc" className="ml-2 block text-sm text-gray-700">
                    Enable shipping calculator on product pages
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    id="require-shipping-address"
                    type="checkbox"
                    checked
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <label htmlFor="require-shipping-address" className="ml-2 block text-sm text-gray-700">
                    Require shipping address for all orders
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    id="enable-local-pickup"
                    type="checkbox"
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <label htmlFor="enable-local-pickup" className="ml-2 block text-sm text-gray-700">
                    Enable local pickup option
                  </label>
                </div>
              </div>
            </div>
            
            {/* Save Button */}
            <div className="pt-4 border-t border-gray-100">
              <button className="px-4 py-2 bg-blue-600 rounded-md text-sm font-medium text-white hover:bg-blue-700">
                Save Settings
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 