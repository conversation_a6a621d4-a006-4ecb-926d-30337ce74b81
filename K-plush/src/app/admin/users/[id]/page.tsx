'use client';

import { useState, use } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { 
  ArrowLeftIcon,
  PencilIcon,
  EnvelopeIcon,
  PhoneIcon,
  ShoppingBagIcon,
  ClockIcon,
  TrashIcon,
  UserCircleIcon
} from '@heroicons/react/24/outline';

// Mock user data
const userData = {
  id: 1,
  name: '<PERSON>',
  email: '<EMAIL>',
  phone: '+****************',
  role: 'admin',
  status: 'active',
  lastLogin: '2024-01-15T10:30:00',
  created: '2023-10-05T08:20:00',
  avatar: '/images/team/placeholder.jpg',
  address: {
    street: '123 Main St',
    city: 'New York',
    state: 'NY',
    zip: '10001',
    country: 'United States'
  },
  orders: [
    {
      id: 'ORD-001',
      date: '2024-01-15T10:30:00',
      total: 299.99,
      status: 'delivered'
    },
    {
      id: 'ORD-012',
      date: '2023-12-20T14:45:00',
      total: 149.99,
      status: 'delivered'
    },
    {
      id: 'ORD-024',
      date: '2023-11-05T09:15:00',
      total: 89.99,
      status: 'delivered'
    }
  ],
  activity: [
    {
      id: 1,
      type: 'login',
      date: '2024-01-15T10:30:00',
      details: 'Logged in from Chrome on Windows'
    },
    {
      id: 2,
      type: 'order_placed',
      date: '2024-01-15T10:35:00',
      details: 'Placed order #ORD-001',
      orderId: 'ORD-001'
    },
    {
      id: 3,
      type: 'profile_update',
      date: '2023-12-18T16:20:00',
      details: 'Updated shipping address'
    },
    {
      id: 4,
      type: 'login',
      date: '2023-12-20T14:40:00',
      details: 'Logged in from Safari on iPhone'
    },
    {
      id: 5,
      type: 'order_placed',
      date: '2023-12-20T14:45:00',
      details: 'Placed order #ORD-012',
      orderId: 'ORD-012'
    },
    {
      id: 6,
      type: 'login',
      date: '2023-11-05T09:10:00',
      details: 'Logged in from Chrome on Mac'
    },
    {
      id: 7,
      type: 'order_placed',
      date: '2023-11-05T09:15:00',
      details: 'Placed order #ORD-024',
      orderId: 'ORD-024'
    }
  ]
};

// Status badge component
const StatusBadge = ({ status }: { status: string }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-50 text-green-700 border-green-200';
      case 'inactive':
        return 'bg-gray-50 text-gray-700 border-gray-200';
      case 'suspended':
        return 'bg-red-50 text-red-700 border-red-200';
      case 'pending':
        return 'bg-amber-50 text-amber-700 border-amber-200';
      case 'processing':
        return 'bg-blue-50 text-blue-700 border-blue-200';
      case 'shipped':
        return 'bg-indigo-50 text-indigo-700 border-indigo-200';
      case 'delivered':
        return 'bg-green-50 text-green-700 border-green-200';
      case 'cancelled':
        return 'bg-red-50 text-red-700 border-red-200';
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };

  return (
    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-md border ${getStatusColor(status)}`}>
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  );
};

// Role badge component
const RoleBadge = ({ role }: { role: string }) => {
  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-purple-50 text-purple-700 border-purple-200';
      case 'moderator':
        return 'bg-blue-50 text-blue-700 border-blue-200';
      case 'customer':
        return 'bg-gray-50 text-gray-700 border-gray-200';
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };

  return (
    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-md border ${getRoleColor(role)}`}>
      {role.charAt(0).toUpperCase() + role.slice(1)}
    </span>
  );
};

// Activity icon component
const ActivityIcon = ({ type }: { type: string }) => {
  switch (type) {
    case 'login':
      return <UserCircleIcon className="h-6 w-6 text-blue-500" />;
    case 'order_placed':
      return <ShoppingBagIcon className="h-6 w-6 text-green-500" />;
    case 'profile_update':
      return <PencilIcon className="h-6 w-6 text-purple-500" />;
    default:
      return <ClockIcon className="h-6 w-6 text-gray-500" />;
  }
};

export default function UserDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = use(params);
  const [activeTab, setActiveTab] = useState('profile');
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  // Handle user deletion
  const handleDeleteUser = () => {
    // In a real app, you would call an API to delete the user
    console.log('Delete user:', id);
    // Redirect to users list
    window.location.href = '/admin/users';
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center">
          <Link href="/admin/users" className="mr-4 p-1 rounded-full hover:bg-gray-100">
            <ArrowLeftIcon className="h-5 w-5 text-gray-500" />
          </Link>
          <div>
            <div className="flex items-center gap-3">
              <h1 className="text-2xl font-medium text-gray-900">
                User Profile
              </h1>
              <StatusBadge status={userData.status} />
            </div>
            <p className="text-sm text-gray-500 mt-1">
              Member since {formatDate(userData.created)}
            </p>
          </div>
        </div>
        <div className="flex flex-wrap gap-3">
          <Link 
            href={`/admin/users/${id}/edit`}
            className="flex items-center px-4 py-2 border border-gray-200 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            <PencilIcon className="h-4 w-4 mr-1.5" />
            Edit
          </Link>
          <button 
            onClick={() => setShowDeleteConfirmation(true)}
            className="flex items-center px-4 py-2 bg-red-600 rounded-md text-sm font-medium text-white hover:bg-red-700"
          >
            <TrashIcon className="h-4 w-4 mr-1.5" />
            Delete User
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('profile')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'profile'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Profile
          </button>
          <button
            onClick={() => setActiveTab('orders')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'orders'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Orders
          </button>
          <button
            onClick={() => setActiveTab('activity')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'activity'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Activity
          </button>
        </nav>
      </div>

      {/* Content based on active tab */}
      <div className="space-y-6">
        {activeTab === 'profile' && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* User Avatar and Basic Info */}
            <div className="bg-white rounded-lg border border-gray-100 p-6">
              <div className="flex flex-col items-center">
                <div className="h-24 w-24 rounded-full overflow-hidden relative mb-4">
                  <Image
                    src={userData.avatar}
                    alt={userData.name}
                    fill
                    className="object-cover"
                  />
                </div>
                <h2 className="text-lg font-medium text-gray-900">{userData.name}</h2>
                <div className="mt-1">
                  <RoleBadge role={userData.role} />
                </div>
                <div className="mt-4 flex items-center text-sm text-gray-500">
                  <EnvelopeIcon className="h-4 w-4 mr-1" />
                  {userData.email}
                </div>
                <div className="mt-2 flex items-center text-sm text-gray-500">
                  <PhoneIcon className="h-4 w-4 mr-1" />
                  {userData.phone}
                </div>
                <div className="mt-4 w-full border-t border-gray-100 pt-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Last Login</span>
                    <span className="text-gray-900">{formatDate(userData.lastLogin)}</span>
                  </div>
                  <div className="flex justify-between text-sm mt-2">
                    <span className="text-gray-500">Status</span>
                    <StatusBadge status={userData.status} />
                  </div>
                  <div className="flex justify-between text-sm mt-2">
                    <span className="text-gray-500">Orders</span>
                    <span className="text-gray-900">{userData.orders.length}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* User Address */}
            <div className="bg-white rounded-lg border border-gray-100 p-6">
              <h3 className="text-sm font-medium text-gray-900 mb-4">Address Information</h3>
              <div className="space-y-1 text-sm text-gray-700">
                <p>{userData.address.street}</p>
                <p>{userData.address.city}, {userData.address.state} {userData.address.zip}</p>
                <p>{userData.address.country}</p>
              </div>
            </div>

            {/* Account Actions */}
            <div className="bg-white rounded-lg border border-gray-100 p-6">
              <h3 className="text-sm font-medium text-gray-900 mb-4">Account Actions</h3>
              <div className="space-y-3">
                <button className="w-full px-4 py-2 bg-blue-50 border border-blue-200 rounded-md text-sm font-medium text-blue-700 hover:bg-blue-100">
                  Reset Password
                </button>
                {userData.status === 'active' ? (
                  <button className="w-full px-4 py-2 bg-amber-50 border border-amber-200 rounded-md text-sm font-medium text-amber-700 hover:bg-amber-100">
                    Suspend Account
                  </button>
                ) : userData.status === 'suspended' ? (
                  <button className="w-full px-4 py-2 bg-green-50 border border-green-200 rounded-md text-sm font-medium text-green-700 hover:bg-green-100">
                    Reactivate Account
                  </button>
                ) : (
                  <button className="w-full px-4 py-2 bg-green-50 border border-green-200 rounded-md text-sm font-medium text-green-700 hover:bg-green-100">
                    Activate Account
                  </button>
                )}
                <button className="w-full px-4 py-2 bg-gray-50 border border-gray-200 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-100">
                  Send Verification Email
                </button>
                <button className="w-full px-4 py-2 bg-red-50 border border-red-200 rounded-md text-sm font-medium text-red-700 hover:bg-red-100">
                  Delete Account
                </button>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'orders' && (
          <div className="bg-white rounded-lg border border-gray-100">
            <div className="px-6 py-4 border-b border-gray-100">
              <h3 className="text-base font-medium text-gray-900">
                Order History
              </h3>
            </div>
            <div className="overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-100">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Order ID
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Total
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-100">
                    {userData.orders.map((order) => (
                      <tr key={order.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {order.id}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(order.date)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                          ${order.total.toFixed(2)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <StatusBadge status={order.status} />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-right">
                          <Link href={`/admin/orders/${order.id}`} className="text-blue-600 hover:text-blue-800">
                            View
                          </Link>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
            {userData.orders.length === 0 && (
              <div className="px-6 py-12 text-center">
                <ShoppingBagIcon className="mx-auto h-12 w-12 text-gray-300" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No orders</h3>
                <p className="mt-1 text-sm text-gray-500">This user hasn&apos;t placed any orders yet.</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'activity' && (
          <div className="bg-white rounded-lg border border-gray-100">
            <div className="px-6 py-4 border-b border-gray-100">
              <h3 className="text-base font-medium text-gray-900">
                Activity Log
              </h3>
            </div>
            <div className="p-6">
              <div className="flow-root">
                <ul className="-mb-8">
                  {userData.activity.map((activity, activityIdx) => (
                    <li key={activity.id}>
                      <div className="relative pb-8">
                        {activityIdx !== userData.activity.length - 1 ? (
                          <span className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true" />
                        ) : null}
                        <div className="relative flex space-x-3">
                          <div>
                            <span className="h-8 w-8 rounded-full flex items-center justify-center bg-gray-100">
                              <ActivityIcon type={activity.type} />
                            </span>
                          </div>
                          <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                            <div>
                              <p className="text-sm text-gray-700">
                                {activity.details}{' '}
                                {activity.orderId && (
                                  <Link href={`/admin/orders/${activity.orderId}`} className="text-blue-600 hover:text-blue-800">
                                    {activity.orderId}
                                  </Link>
                                )}
                              </p>
                            </div>
                            <div className="text-right text-sm whitespace-nowrap text-gray-500">
                              {formatDate(activity.date)}
                            </div>
                          </div>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirmation && (
        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <TrashIcon className="h-6 w-6 text-red-600" aria-hidden="true" />
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      Delete User
                    </h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        Are you sure you want to delete this user? All of their data will be permanently removed.
                        This action cannot be undone.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  onClick={handleDeleteUser}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Delete
                </button>
                <button
                  type="button"
                  onClick={() => setShowDeleteConfirmation(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 