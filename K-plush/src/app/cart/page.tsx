import Image from "next/image";
import Link from "next/link";
import { getImageUrl } from "@/lib/placeholder-images";

export default function CartPage() {
  // This would normally come from a state management solution or API
  const cartItems = [
    {
      id: "modern-sofa-1",
      name: "Modern Comfort Sofa",
      price: 1299.99,
      image: getImageUrl('modern-sofa'),
      quantity: 1
    },
    {
      id: "coffee-table-1",
      name: "Minimalist Coffee Table",
      price: 349.99,
      image: getImageUrl('coffee-table'),
      quantity: 1
    }
  ];

  const subtotal = cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  const shipping = 99.99;
  const total = subtotal + shipping;

  return (
    <div className="container mx-auto px-4 py-12">
      <h1 className="text-3xl font-bold text-gray-800 mb-8">Your Cart</h1>

      {cartItems.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-800">Cart Items ({cartItems.length})</h2>
              </div>

              <ul className="divide-y divide-gray-200">
                {cartItems.map((item) => (
                  <li key={item.id} className="p-6 flex flex-col sm:flex-row items-center sm:items-start gap-4">
                    <div className="relative h-24 w-24 flex-shrink-0 rounded-md overflow-hidden">
                      <Image
                        src={item.image}
                        alt={item.name}
                        fill
                        className="object-cover"
                      />
                    </div>

                    <div className="flex-1">
                      <div className="flex flex-col sm:flex-row sm:justify-between">
                        <h3 className="text-base font-medium text-gray-800">
                          <Link href={`/shop/${item.id}`} className="hover:text-amber-500 transition-colors">
                            {item.name}
                          </Link>
                        </h3>
                        <p className="text-base font-medium text-gray-800">${item.price.toFixed(2)}</p>
                      </div>

                      <div className="mt-2 flex flex-col sm:flex-row sm:justify-between sm:items-center">
                        <div className="flex items-center border border-gray-300 rounded-md w-32">
                          <button className="px-3 py-1 text-gray-600 hover:text-amber-500 transition-colors">
                            -
                          </button>
                          <input
                            type="number"
                            min="1"
                            value={item.quantity}
                            readOnly
                            className="w-full text-center border-0 focus:ring-0"
                          />
                          <button className="px-3 py-1 text-gray-600 hover:text-amber-500 transition-colors">
                            +
                          </button>
                        </div>

                        <button className="mt-2 sm:mt-0 text-sm text-red-500 hover:text-red-600 transition-colors">
                          Remove
                        </button>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>

              <div className="p-6 border-t border-gray-200">
                <Link
                  href="/shop"
                  className="text-amber-500 hover:text-amber-600 transition-colors flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                  </svg>
                  Continue Shopping
                </Link>
              </div>
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm overflow-hidden sticky top-4">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-800">Order Summary</h2>
              </div>

              <div className="p-6 space-y-4">
                <div className="flex justify-between">
                  <p className="text-gray-600">Subtotal</p>
                  <p className="text-gray-800 font-medium">${subtotal.toFixed(2)}</p>
                </div>

                <div className="flex justify-between">
                  <p className="text-gray-600">Shipping</p>
                  <p className="text-gray-800 font-medium">${shipping.toFixed(2)}</p>
                </div>

                <div className="border-t border-gray-200 pt-4 flex justify-between">
                  <p className="text-gray-800 font-semibold">Total</p>
                  <p className="text-gray-800 font-semibold">${total.toFixed(2)}</p>
                </div>

                <button className="w-full bg-amber-500 text-white py-3 px-4 rounded-md font-medium hover:bg-amber-600 transition-colors">
                  Proceed to Checkout
                </button>

                <div className="mt-4">
                  <h3 className="text-sm font-medium text-gray-800 mb-2">We Accept</h3>
                  <div className="flex space-x-2">
                    <div className="h-8 w-12 bg-gray-200 rounded"></div>
                    <div className="h-8 w-12 bg-gray-200 rounded"></div>
                    <div className="h-8 w-12 bg-gray-200 rounded"></div>
                    <div className="h-8 w-12 bg-gray-200 rounded"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="text-center py-16 bg-white rounded-lg shadow-sm">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
          <h2 className="text-xl font-medium text-gray-800 mb-2">Your cart is empty</h2>
          <p className="text-gray-600 mb-6">
            Looks like you haven&apos;t added any furniture to your cart yet.
          </p>
          <Link
            href="/shop"
            className="bg-amber-500 text-white px-6 py-2 rounded-md font-medium hover:bg-amber-600 transition-colors inline-block"
          >
            Start Shopping
          </Link>
        </div>
      )}
    </div>
  );
}
