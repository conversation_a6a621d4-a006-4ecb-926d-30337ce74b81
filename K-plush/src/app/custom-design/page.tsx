import Image from "next/image";
import { getImageUrl } from "@/lib/placeholder-images";

export default function CustomDesignPage() {
  return (
    <div className="min-h-screen bg-background relative overflow-hidden">
      {/* Decorative background elements */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-20 left-1/4 w-96 h-96 rounded-full bg-primary opacity-5 blur-3xl"></div>
        <div className="absolute bottom-20 right-1/4 w-80 h-80 rounded-full bg-accent-light opacity-5 blur-3xl"></div>
        <div className="absolute top-1/2 left-0 w-64 h-64 rounded-full bg-primary-light opacity-3 blur-2xl"></div>
      </div>

      <div className="relative">
        {/* Hero Section */}
        <section className="py-20 relative">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <span className="inline-block px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-6 animate-fade-in">
                Bespoke Craftsmanship
              </span>
              
              <h1 className="font-serif text-4xl md:text-5xl lg:text-6xl font-medium text-foreground mb-6 animate-fade-in" style={{ animationDelay: '0.2s' }}>
                <span className="block relative">
                  <span className="relative z-10">Bring Your Vision</span>
                  <span className="absolute bottom-1 left-0 h-3 w-full bg-primary/20 -z-10 transform -skew-x-3"></span>
                </span>
                <span className="block mt-2">to Life</span>
              </h1>
              
              <p className="text-xl text-foreground/70 max-w-3xl mx-auto mb-12 leading-relaxed animate-fade-in" style={{ animationDelay: '0.4s' }}>
                Our master craftsmen transform your unique ideas into exceptional furniture pieces. 
                Every detail is carefully considered, every material thoughtfully selected.
              </p>
            </div>
          </div>
        </section>

        {/* Main Content */}
        <section className="pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-6xl mx-auto">
              
              {/* Process Overview */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 mb-20">
                {/* Left side - Process */}
                <div className="animate-fade-in" style={{ animationDelay: '0.6s' }}>
                  <div className="relative">
                    <h2 className="font-serif text-3xl font-medium text-foreground mb-8 relative inline-block">
                      Our Process
                      <span className="absolute -bottom-2 left-0 w-16 h-1 bg-primary"></span>
                    </h2>
                  </div>
                  
                  <div className="space-y-8">
                    {[
                      { 
                        step: "01", 
                        title: "Design Consultation", 
                        desc: "Share your vision through detailed discussions and inspiration sharing." 
                      },
                      { 
                        step: "02", 
                        title: "Concept Development", 
                        desc: "We create detailed sketches and 3D visualizations of your piece." 
                      },
                      { 
                        step: "03", 
                        title: "Material Selection", 
                        desc: "Choose from premium materials with guidance from our experts." 
                      },
                      { 
                        step: "04", 
                        title: "Masterful Crafting", 
                        desc: "Our artisans bring your design to life with exceptional attention to detail." 
                      }
                    ].map((item, index) => (
                      <div key={index} className="flex items-start group">
                        <div className="flex-shrink-0 mr-6">
                          <div className="relative">
                            <div className="w-12 h-12 rounded-full bg-primary/10 group-hover:bg-primary/20 transition-colors duration-300 flex items-center justify-center">
                              <span className="font-serif text-primary font-medium">{item.step}</span>
                            </div>
                            {index < 3 && (
                              <div className="absolute top-12 left-1/2 w-px h-8 bg-primary/20 transform -translate-x-1/2"></div>
                            )}
                          </div>
                        </div>
                        <div className="flex-1 pt-2">
                          <h3 className="font-medium text-foreground mb-2 group-hover:text-primary transition-colors duration-300">
                            {item.title}
                          </h3>
                          <p className="text-foreground/70 text-sm leading-relaxed">{item.desc}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Right side - Image */}
                <div className="animate-fade-in" style={{ animationDelay: '0.8s' }}>
                  <div className="relative h-[500px] rounded-2xl overflow-hidden">
                    <Image
                      src={getImageUrl('custom-design')}
                      alt="Custom Furniture Crafting"
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"></div>
                    
                    {/* Floating badge */}
                    <div className="absolute bottom-6 left-6 bg-white/95 backdrop-blur-sm rounded-lg p-4 max-w-[250px] shadow-lg">
                      <div className="flex items-center mb-2">
                        <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                        <span className="text-sm font-medium text-gray-900">Premium Quality Guaranteed</span>
                      </div>
                      <p className="text-xs text-gray-600">Every piece comes with our lifetime craftsmanship warranty</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Design Request Form */}
              <div className="animate-fade-in" style={{ animationDelay: '1s' }}>
                <div className="relative">
                  {/* Form Header */}
                  <div className="text-center mb-12">
                    <h2 className="font-serif text-3xl md:text-4xl font-medium text-foreground mb-4 relative inline-block">
                      Start Your Custom Project
                      <span className="absolute -bottom-2 left-1/2 w-20 h-1 bg-primary transform -translate-x-1/2"></span>
                    </h2>
                    <p className="text-foreground/70 max-w-2xl mx-auto">
                      Share your vision with us, and let&apos;s create something extraordinary together.
                    </p>
                  </div>

                  {/* Clean Form Container */}
                  <div className="max-w-4xl mx-auto">
                    <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
                      <form className="p-8 md:p-12 space-y-8">
                        {/* Personal Information */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <label className="block text-sm font-semibold text-gray-900 dark:text-white mb-2">
                              Full Name <span className="text-red-500">*</span>
                            </label>
                            <input
                              type="text"
                              className="w-full px-4 py-3 bg-gray-50 dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-primary focus:ring-0 focus:bg-white dark:focus:bg-gray-700 transition-all duration-200"
                              placeholder="Enter your full name"
                              required
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-semibold text-gray-900 dark:text-white mb-2">
                              Email Address <span className="text-red-500">*</span>
                            </label>
                            <input
                              type="email"
                              className="w-full px-4 py-3 bg-gray-50 dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-primary focus:ring-0 focus:bg-white dark:focus:bg-gray-700 transition-all duration-200"
                              placeholder="<EMAIL>"
                              required
                            />
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <label className="block text-sm font-semibold text-gray-900 dark:text-white mb-2">
                              Phone Number
                            </label>
                            <input
                              type="tel"
                              className="w-full px-4 py-3 bg-gray-50 dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-primary focus:ring-0 focus:bg-white dark:focus:bg-gray-700 transition-all duration-200"
                              placeholder="+****************"
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-semibold text-gray-900 dark:text-white mb-2">
                              Furniture Category <span className="text-red-500">*</span>
                            </label>
                            <select className="w-full px-4 py-3 bg-gray-50 dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white focus:border-primary focus:ring-0 focus:bg-white dark:focus:bg-gray-700 transition-all duration-200" required>
                              <option value="">Select category</option>
                              <option value="seating">Seating & Sofas</option>
                              <option value="tables">Tables & Desks</option>
                              <option value="storage">Storage & Cabinets</option>
                              <option value="bedroom">Bedroom Furniture</option>
                              <option value="outdoor">Outdoor Furniture</option>
                              <option value="other">Other / Mixed</option>
                            </select>
                          </div>
                        </div>

                        {/* Project Description */}
                        <div>
                          <label className="block text-sm font-semibold text-gray-900 dark:text-white mb-2">
                            Project Description <span className="text-red-500">*</span>
                          </label>
                          <textarea
                            rows={5}
                            className="w-full px-4 py-3 bg-gray-50 dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-primary focus:ring-0 focus:bg-white dark:focus:bg-gray-700 transition-all duration-200 resize-vertical"
                            placeholder="Describe your vision in detail... Include dimensions, materials, style preferences, and any specific requirements or inspirations."
                            required
                          ></textarea>
                        </div>

                        {/* File Upload */}
                        <div>
                          <label className="block text-sm font-semibold text-gray-900 dark:text-white mb-2">
                            Inspiration Images
                          </label>
                          <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center bg-gray-50 dark:bg-gray-800 hover:border-primary dark:hover:border-primary transition-colors duration-200">
                            <div className="space-y-4">
                              <div className="mx-auto w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                                <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                              </div>
                              <div>
                                <label htmlFor="file-upload" className="cursor-pointer">
                                  <span className="text-primary font-semibold hover:text-primary-dark transition-colors">
                                    Upload images
                                  </span>
                                  <span className="text-gray-600 dark:text-gray-400"> or drag and drop</span>
                                  <input id="file-upload" type="file" className="sr-only" multiple accept="image/*" />
                                </label>
                              </div>
                              <p className="text-sm text-gray-500 dark:text-gray-400">PNG, JPG, GIF up to 10MB each</p>
                            </div>
                          </div>
                        </div>

                        {/* Budget */}
                        <div>
                          <label className="block text-sm font-semibold text-gray-900 dark:text-white mb-2">
                            Investment Range <span className="text-red-500">*</span>
                          </label>
                          <select className="w-full px-4 py-3 bg-gray-50 dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white focus:border-primary focus:ring-0 focus:bg-white dark:focus:bg-gray-700 transition-all duration-200" required>
                            <option value="">Select your budget range</option>
                            <option value="under-1000">Under $1,000</option>
                            <option value="1000-2500">$1,000 - $2,500</option>
                            <option value="2500-5000">$2,500 - $5,000</option>
                            <option value="5000-10000">$5,000 - $10,000</option>
                            <option value="over-10000">$10,000+</option>
                            <option value="discuss">Prefer to discuss</option>
                          </select>
                        </div>

                        {/* Submit Button */}
                        <div className="pt-6">
                          <button
                            type="submit"
                            className="w-full bg-primary hover:bg-primary-dark text-white font-semibold py-4 px-8 rounded-lg transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-4 focus:ring-primary/30 shadow-lg hover:shadow-xl"
                          >
                            <span className="flex items-center justify-center">
                              <span className="mr-2">Begin My Custom Journey</span>
                              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                              </svg>
                            </span>
                          </button>
                          
                          {/* Trust indicators */}
                          <div className="flex items-center justify-center mt-6 space-x-6 text-sm text-gray-600 dark:text-gray-400">
                            <div className="flex items-center">
                              <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                              Free Consultation
                            </div>
                            <div className="flex items-center">
                              <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                              No Obligation
                            </div>
                            <div className="flex items-center">
                              <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                              Expert Guidance
                            </div>
                          </div>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
