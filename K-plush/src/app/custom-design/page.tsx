import Image from "next/image";
import { getImageUrl } from "@/lib/placeholder-images";

export default function CustomDesignPage() {

  return (
    <div className="min-h-screen bg-background relative overflow-hidden">
      {/* Minimalistic background elements */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-1/4 right-1/4 w-72 h-72 rounded-full bg-primary opacity-3 blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/3 left-1/5 w-96 h-96 rounded-full bg-accent-light opacity-2 blur-3xl"></div>
      </div>

      <div className="relative">
        {/* Minimalistic Hero Section */}
        <section className="py-16 relative">
          <div className="container mx-auto px-6">
            <div className="max-w-6xl mx-auto">
              {/* Header */}
              <div className="text-center mb-16">
                <div className="inline-flex items-center px-4 py-2 rounded-full bg-primary/8 text-primary text-sm font-medium mb-6 animate-fade-in">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  Custom Design Studio
                </div>

                <h1 className="font-serif text-3xl md:text-4xl lg:text-5xl font-medium text-foreground mb-4 animate-fade-in">
                  Create Something
                  <span className="block text-primary">Extraordinary</span>
                </h1>

                <p className="text-foreground/70 text-lg max-w-2xl mx-auto animate-fade-in">
                  Transform your vision into reality with our step-by-step design process
                </p>
              </div>

              <p className="text-xl text-foreground/70 max-w-3xl mx-auto mb-12 leading-relaxed animate-fade-in">
                Our master craftsmen transform your unique ideas into exceptional furniture pieces.
                Every detail is carefully considered, every material thoughtfully selected.
              </p>
            </div>
          </div>
        </section>

        {/* Main Content */}
        <section className="pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-6xl mx-auto">
              
              {/* Process Overview */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 mb-20">
                {/* Left side - Process */}
                <div className="animate-fade-in">
                  <div className="relative">
                    <h2 className="font-serif text-3xl font-medium text-foreground mb-8 relative inline-block">
                      Our Process
                      <span className="absolute -bottom-2 left-0 w-16 h-1 bg-primary"></span>
                    </h2>
                  </div>
                  
                  <div className="space-y-8">
                    {[
                      { 
                        step: "01", 
                        title: "Design Consultation", 
                        desc: "Share your vision through detailed discussions and inspiration sharing." 
                      },
                      { 
                        step: "02", 
                        title: "Concept Development", 
                        desc: "We create detailed sketches and 3D visualizations of your piece." 
                      },
                      { 
                        step: "03", 
                        title: "Material Selection", 
                        desc: "Choose from premium materials with guidance from our experts." 
                      },
                      { 
                        step: "04", 
                        title: "Masterful Crafting", 
                        desc: "Our artisans bring your design to life with exceptional attention to detail." 
                      }
                    ].map((item, index) => (
                      <div key={index} className="flex items-start group">
                        <div className="flex-shrink-0 mr-6">
                          <div className="relative">
                            <div className="w-12 h-12 rounded-full bg-primary/10 group-hover:bg-primary/20 transition-colors duration-300 flex items-center justify-center">
                              <span className="font-serif text-primary font-medium">{item.step}</span>
                            </div>
                            {index < 3 && (
                              <div className="absolute top-12 left-1/2 w-px h-8 bg-primary/20 transform -translate-x-1/2"></div>
                            )}
                          </div>
                        </div>
                        <div className="flex-1 pt-2">
                          <h3 className="font-medium text-foreground mb-2 group-hover:text-primary transition-colors duration-300">
                            {item.title}
                          </h3>
                          <p className="text-foreground/70 text-sm leading-relaxed">{item.desc}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Right side - Image */}
                <div className="animate-fade-in">
                  <div className="relative h-[500px] rounded-2xl overflow-hidden">
                    <Image
                      src={getImageUrl('custom-design')}
                      alt="Custom Furniture Crafting"
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"></div>
                    
                    {/* Floating badge */}
                    <div className="absolute bottom-6 left-6 bg-white/95 backdrop-blur-sm rounded-lg p-4 max-w-[250px] shadow-lg">
                      <div className="flex items-center mb-2">
                        <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                        <span className="text-sm font-medium text-gray-900">Premium Quality Guaranteed</span>
                      </div>
                      <p className="text-xs text-gray-600">Every piece comes with our lifetime craftsmanship warranty</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Simple Contact Form */}
              <div className="max-w-4xl mx-auto animate-fade-in">
                <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/20 overflow-hidden">
                  <div className="p-8 md:p-12">
                    <div className="text-center mb-8">
                      <h3 className="font-serif text-2xl font-medium text-foreground mb-3">
                        Start Your Custom Project
                      </h3>
                      <p className="text-foreground/70">
                        Get in touch to discuss your vision
                      </p>
                    </div>

                    <form className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-foreground mb-2">
                            Full Name *
                          </label>
                          <input
                            type="text"
                            className="w-full px-4 py-3 bg-white/70 border border-kgray-light rounded-xl text-foreground placeholder-foreground/50 focus:border-primary focus:ring-0 focus:bg-white transition-all duration-200"
                            placeholder="Enter your full name"
                            required
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-foreground mb-2">
                            Email Address *
                          </label>
                          <input
                            type="email"
                            className="w-full px-4 py-3 bg-white/70 border border-kgray-light rounded-xl text-foreground placeholder-foreground/50 focus:border-primary focus:ring-0 focus:bg-white transition-all duration-200"
                            placeholder="<EMAIL>"
                            required
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-foreground mb-2">
                          Project Description *
                        </label>
                        <textarea
                          rows={4}
                          className="w-full px-4 py-3 bg-white/70 border border-kgray-light rounded-xl text-foreground placeholder-foreground/50 focus:border-primary focus:ring-0 focus:bg-white transition-all duration-200 resize-none"
                          placeholder="Tell us about your dream piece..."
                          required
                        />
                      </div>

                      <div className="text-center">
                        <button
                          type="submit"
                          className="px-8 py-3 bg-primary hover:bg-primary-dark text-white rounded-xl font-medium transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                        >
                          Start My Project
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
