'use client';

import { useState, useEffect } from 'react';
import Image from "next/image";
import { getImageUrl } from "@/lib/placeholder-images";

export default function CustomDesignPage() {
  const [activeStep, setActiveStep] = useState(0);
  const [selectedStyle, setSelectedStyle] = useState('');
  const [isAnimating, setIsAnimating] = useState(false);

  const designStyles = [
    {
      id: 'minimalist',
      title: 'Minimalist',
      description: 'Clean lines, neutral colors, functional beauty',
      icon: '◯',
      color: 'bg-slate-100 hover:bg-slate-200',
      accent: 'border-slate-400'
    },
    {
      id: 'industrial',
      title: 'Industrial',
      description: 'Raw materials, exposed elements, urban edge',
      icon: '⬢',
      color: 'bg-zinc-100 hover:bg-zinc-200',
      accent: 'border-zinc-400'
    },
    {
      id: 'scandinavian',
      title: 'Scandinavian',
      description: 'Light woods, cozy textures, hygge vibes',
      icon: '△',
      color: 'bg-amber-50 hover:bg-amber-100',
      accent: 'border-amber-400'
    },
    {
      id: 'bohemian',
      title: 'Bohemian',
      description: 'Rich patterns, warm colors, eclectic mix',
      icon: '✦',
      color: 'bg-rose-50 hover:bg-rose-100',
      accent: 'border-rose-400'
    },
    {
      id: 'modern',
      title: 'Modern',
      description: 'Bold geometry, statement pieces, innovation',
      icon: '◆',
      color: 'bg-blue-50 hover:bg-blue-100',
      accent: 'border-blue-400'
    },
    {
      id: 'rustic',
      title: 'Rustic',
      description: 'Natural wood, handcrafted details, warmth',
      icon: '⬟',
      color: 'bg-green-50 hover:bg-green-100',
      accent: 'border-green-400'
    }
  ];

  const steps = [
    { title: 'Style', subtitle: 'Choose your aesthetic' },
    { title: 'Space', subtitle: 'Define your room' },
    { title: 'Details', subtitle: 'Share your vision' },
    { title: 'Connect', subtitle: 'Let\'s collaborate' }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 via-white to-stone-50">
      {/* Artistic Header */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-accent/5"></div>
        <div className="container mx-auto px-6 py-20">
          <div className="text-center space-y-8">
            <div className="relative inline-block">
              <h1 className="text-6xl md:text-8xl font-light text-gray-900 tracking-tight">
                Craft
              </h1>
              <div className="absolute -top-4 -right-8 w-16 h-16 border-2 border-primary/30 rounded-full animate-spin-slow"></div>
              <div className="absolute -bottom-2 -left-6 w-8 h-8 bg-accent/20 rounded-full"></div>
            </div>

            <div className="flex items-center justify-center space-x-8">
              <div className="w-24 h-px bg-gradient-to-r from-transparent to-gray-300"></div>
              <span className="text-2xl font-light text-gray-600 tracking-widest">YOUR VISION</span>
              <div className="w-24 h-px bg-gradient-to-l from-transparent to-gray-300"></div>
            </div>

            <p className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
              An immersive journey through bespoke furniture design, where your imagination meets our craftsmanship
            </p>
          </div>
        </div>
      </div>

      {/* Progress Steps */}
      <div className="container mx-auto px-6 py-12">
        <div className="flex justify-center mb-16">
          <div className="flex items-center space-x-8">
            {steps.map((step, index) => (
              <div key={index} className="flex items-center">
                <div className={`relative flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-500 ${
                  index <= activeStep
                    ? 'bg-primary border-primary text-white'
                    : 'bg-white border-gray-300 text-gray-400'
                }`}>
                  <span className="text-sm font-medium">{index + 1}</span>
                  {index < activeStep && (
                    <div className="absolute inset-0 bg-primary rounded-full animate-pulse"></div>
                  )}
                </div>
                <div className="ml-3 text-left">
                  <div className={`text-sm font-medium ${index <= activeStep ? 'text-primary' : 'text-gray-400'}`}>
                    {step.title}
                  </div>
                  <div className="text-xs text-gray-500">{step.subtitle}</div>
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-16 h-px mx-6 transition-all duration-500 ${
                    index < activeStep ? 'bg-primary' : 'bg-gray-200'
                  }`}></div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Style Selection - Step 1 */}
      {activeStep === 0 && (
        <section className="container mx-auto px-6 py-16">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-light text-gray-900 mb-4">
              What speaks to your soul?
            </h2>
            <p className="text-lg text-gray-600 max-w-xl mx-auto">
              Every great piece begins with a feeling. Choose the aesthetic that resonates with your vision.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {designStyles.map((style, index) => (
              <div
                key={style.id}
                onClick={() => {
                  setSelectedStyle(style.id);
                  setTimeout(() => setActiveStep(1), 800);
                }}
                className={`group relative p-8 rounded-2xl border-2 cursor-pointer transition-all duration-500 hover:scale-105 ${
                  selectedStyle === style.id
                    ? `${style.color} ${style.accent} shadow-2xl scale-105`
                    : `${style.color} border-gray-200 hover:shadow-xl`
                }`}
              >
                <div className="text-center space-y-4">
                  <div className="text-6xl font-light text-gray-700 group-hover:scale-110 transition-transform duration-300">
                    {style.icon}
                  </div>
                  <h3 className="text-2xl font-medium text-gray-900">{style.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{style.description}</p>

                  {selectedStyle === style.id && (
                    <div className="absolute top-4 right-4">
                      <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center">
                        <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Space Definition - Step 2 */}
      {activeStep === 1 && (
        <section className="container mx-auto px-6 py-16">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-light text-gray-900 mb-4">
                Tell us about your space
              </h2>
              <p className="text-lg text-gray-600">
                Understanding your environment helps us create the perfect piece for your home.
              </p>
            </div>

            <div className="bg-white rounded-3xl p-8 shadow-lg">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">Room Type</label>
                    <div className="grid grid-cols-2 gap-3">
                      {['Living Room', 'Bedroom', 'Dining Room', 'Office', 'Kitchen', 'Other'].map((room) => (
                        <button
                          key={room}
                          className="p-3 text-left border border-gray-200 rounded-xl hover:border-primary hover:bg-primary/5 transition-all"
                        >
                          {room}
                        </button>
                      ))}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">Space Dimensions</label>
                    <div className="grid grid-cols-2 gap-4">
                      <input
                        type="text"
                        placeholder="Length (ft)"
                        className="p-3 border border-gray-200 rounded-xl focus:border-primary focus:ring-1 focus:ring-primary"
                      />
                      <input
                        type="text"
                        placeholder="Width (ft)"
                        className="p-3 border border-gray-200 rounded-xl focus:border-primary focus:ring-1 focus:ring-primary"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">Furniture Type</label>
                    <select className="w-full p-3 border border-gray-200 rounded-xl focus:border-primary focus:ring-1 focus:ring-primary">
                      <option>Dining Table</option>
                      <option>Sofa/Sectional</option>
                      <option>Bed Frame</option>
                      <option>Desk</option>
                      <option>Storage/Shelving</option>
                      <option>Other</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">Color Preferences</label>
                    <div className="flex flex-wrap gap-2">
                      {['Natural Wood', 'White/Cream', 'Dark/Black', 'Colorful', 'Mixed'].map((color) => (
                        <button
                          key={color}
                          className="px-4 py-2 text-sm border border-gray-200 rounded-full hover:border-primary hover:bg-primary/5 transition-all"
                        >
                          {color}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-center mt-8">
                <button
                  onClick={() => setActiveStep(2)}
                  className="px-8 py-3 bg-primary text-white rounded-xl hover:bg-primary-dark transition-all"
                >
                  Continue to Details
                </button>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Vision Details - Step 3 */}
      {activeStep === 2 && (
        <section className="container mx-auto px-6 py-16">
          <div className="max-w-3xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-light text-gray-900 mb-4">
                Share your vision
              </h2>
              <p className="text-lg text-gray-600">
                The more details you share, the better we can bring your dream to life.
              </p>
            </div>

            <div className="bg-white rounded-3xl p-8 shadow-lg space-y-8">
              <div>
                <label className="block text-lg font-medium text-gray-700 mb-4">Describe your dream piece</label>
                <textarea
                  rows={6}
                  className="w-full p-4 border border-gray-200 rounded-xl focus:border-primary focus:ring-1 focus:ring-primary resize-none"
                  placeholder="Paint us a picture with words... What do you envision? How will it make you feel? What memories will it hold?"
                ></textarea>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <label className="block text-lg font-medium text-gray-700 mb-4">Inspiration & References</label>
                  <div className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-primary transition-colors cursor-pointer">
                    <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                    <p className="text-gray-600">Upload images, sketches, or inspiration</p>
                    <p className="text-sm text-gray-400 mt-2">PNG, JPG up to 10MB</p>
                  </div>
                </div>

                <div>
                  <label className="block text-lg font-medium text-gray-700 mb-4">Special Requirements</label>
                  <div className="space-y-3">
                    {['Eco-friendly materials', 'Pet-friendly design', 'Child-safe features', 'Storage solutions', 'Modular/expandable', 'Other'].map((req) => (
                      <label key={req} className="flex items-center space-x-3">
                        <input type="checkbox" className="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary" />
                        <span className="text-gray-700">{req}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>

              <div className="flex justify-center">
                <button
                  onClick={() => setActiveStep(3)}
                  className="px-8 py-3 bg-primary text-white rounded-xl hover:bg-primary-dark transition-all"
                >
                  Let's Connect
                </button>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Contact & Collaboration - Step 4 */}
      {activeStep === 3 && (
        <section className="container mx-auto px-6 py-16">
          <div className="max-w-3xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-light text-gray-900 mb-4">
                Let's bring it to life
              </h2>
              <p className="text-lg text-gray-600">
                Ready to start this creative journey together? We'll be in touch within 24 hours.
              </p>
            </div>

            <div className="bg-white rounded-3xl p-8 shadow-lg">
              <form className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Full Name <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      className="w-full p-3 border border-gray-200 rounded-xl focus:border-primary focus:ring-1 focus:ring-primary"
                      placeholder="Enter your full name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email Address <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="email"
                      className="w-full p-3 border border-gray-200 rounded-xl focus:border-primary focus:ring-1 focus:ring-primary"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                    <input
                      type="tel"
                      className="w-full p-3 border border-gray-200 rounded-xl focus:border-primary focus:ring-1 focus:ring-primary"
                      placeholder="+****************"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Furniture Category <span className="text-red-500">*</span>
                    </label>
                    <select className="w-full p-3 border border-gray-200 rounded-xl focus:border-primary focus:ring-1 focus:ring-primary">
                      <option>Select category</option>
                      <option>Dining Table</option>
                      <option>Sofa/Sectional</option>
                      <option>Bed Frame</option>
                      <option>Desk</option>
                      <option>Storage/Shelving</option>
                      <option>Other</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Project Description <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    rows={4}
                    className="w-full p-3 border border-gray-200 rounded-xl focus:border-primary focus:ring-1 focus:ring-primary resize-none"
                    placeholder="Describe your vision in detail... Include dimensions, materials, style preferences, and any specific requirements or inspirations."
                  ></textarea>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Inspiration Images</label>
                  <div className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-primary transition-colors cursor-pointer">
                    <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                    <p className="text-gray-600 mb-2">or drag and drop</p>
                    <p className="text-sm text-gray-400">PNG, JPG, GIF up to 10MB each</p>
                  </div>
                </div>

                <div className="pt-4">
                  <button
                    type="submit"
                    className="w-full bg-gradient-to-r from-primary to-accent text-white py-4 rounded-xl font-medium text-lg shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300"
                  >
                    Begin Our Creative Journey Together
                  </button>
                </div>
              </form>
            </div>
          </div>
        </section>
      )}

      {/* Floating Back Button */}
      {activeStep > 0 && (
        <button
          onClick={() => setActiveStep(activeStep - 1)}
          className="fixed bottom-8 left-8 p-4 bg-white border border-gray-200 rounded-full shadow-lg hover:shadow-xl transition-all z-40"
        >
          <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>
      )}
    </div>
  );
}