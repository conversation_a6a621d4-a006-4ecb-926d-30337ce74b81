'use client';

import { useState } from 'react';
import Image from "next/image";
import { getImageUrl } from "@/lib/placeholder-images";

interface FormData {
  // Step 1: Project Type
  category: string;
  projectType: string;

  // Step 2: Vision & Details
  description: string;
  style: string;
  materials: string[];
  dimensions: string;

  // Step 3: Inspiration
  images: File[];
  inspirationNotes: string;

  // Step 4: Contact & Budget
  fullName: string;
  email: string;
  phone: string;
  budget: string;
  timeline: string;
}

export default function CustomDesignPage() {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<FormData>({
    category: '',
    projectType: '',
    description: '',
    style: '',
    materials: [],
    dimensions: '',
    images: [],
    inspirationNotes: '',
    fullName: '',
    email: '',
    phone: '',
    budget: '',
    timeline: ''
  });

  const totalSteps = 4;

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const updateFormData = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="min-h-screen bg-background relative overflow-hidden">
      {/* Minimalistic background elements */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-1/4 right-1/4 w-72 h-72 rounded-full bg-primary opacity-3 blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/3 left-1/5 w-96 h-96 rounded-full bg-accent-light opacity-2 blur-3xl"></div>
      </div>

      <div className="relative">
        {/* Minimalistic Hero Section */}
        <section className="py-16 relative">
          <div className="container mx-auto px-6">
            <div className="max-w-6xl mx-auto">
              {/* Header */}
              <div className="text-center mb-16">
                <div className="inline-flex items-center px-4 py-2 rounded-full bg-primary/8 text-primary text-sm font-medium mb-6 animate-fade-in">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  Custom Design Studio
                </div>

                <h1 className="font-serif text-3xl md:text-4xl lg:text-5xl font-medium text-foreground mb-4 animate-fade-in" style={{ animationDelay: '0.1s' }}>
                  Create Something
                  <span className="block text-primary">Extraordinary</span>
                </h1>

                <p className="text-foreground/70 text-lg max-w-2xl mx-auto animate-fade-in" style={{ animationDelay: '0.2s' }}>
                  Transform your vision into reality with our step-by-step design process
                </p>
              </div>
              
              <p className="text-xl text-foreground/70 max-w-3xl mx-auto mb-12 leading-relaxed animate-fade-in" style={{ animationDelay: '0.4s' }}>
                Our master craftsmen transform your unique ideas into exceptional furniture pieces. 
                Every detail is carefully considered, every material thoughtfully selected.
              </p>
            </div>
          </div>
        </section>

        {/* Main Content */}
        <section className="pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-6xl mx-auto">
              
              {/* Process Overview */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 mb-20">
                {/* Left side - Process */}
                <div className="animate-fade-in" style={{ animationDelay: '0.6s' }}>
                  <div className="relative">
                    <h2 className="font-serif text-3xl font-medium text-foreground mb-8 relative inline-block">
                      Our Process
                      <span className="absolute -bottom-2 left-0 w-16 h-1 bg-primary"></span>
                    </h2>
                  </div>
                  
                  <div className="space-y-8">
                    {[
                      { 
                        step: "01", 
                        title: "Design Consultation", 
                        desc: "Share your vision through detailed discussions and inspiration sharing." 
                      },
                      { 
                        step: "02", 
                        title: "Concept Development", 
                        desc: "We create detailed sketches and 3D visualizations of your piece." 
                      },
                      { 
                        step: "03", 
                        title: "Material Selection", 
                        desc: "Choose from premium materials with guidance from our experts." 
                      },
                      { 
                        step: "04", 
                        title: "Masterful Crafting", 
                        desc: "Our artisans bring your design to life with exceptional attention to detail." 
                      }
                    ].map((item, index) => (
                      <div key={index} className="flex items-start group">
                        <div className="flex-shrink-0 mr-6">
                          <div className="relative">
                            <div className="w-12 h-12 rounded-full bg-primary/10 group-hover:bg-primary/20 transition-colors duration-300 flex items-center justify-center">
                              <span className="font-serif text-primary font-medium">{item.step}</span>
                            </div>
                            {index < 3 && (
                              <div className="absolute top-12 left-1/2 w-px h-8 bg-primary/20 transform -translate-x-1/2"></div>
                            )}
                          </div>
                        </div>
                        <div className="flex-1 pt-2">
                          <h3 className="font-medium text-foreground mb-2 group-hover:text-primary transition-colors duration-300">
                            {item.title}
                          </h3>
                          <p className="text-foreground/70 text-sm leading-relaxed">{item.desc}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Right side - Image */}
                <div className="animate-fade-in" style={{ animationDelay: '0.8s' }}>
                  <div className="relative h-[500px] rounded-2xl overflow-hidden">
                    <Image
                      src={getImageUrl('custom-design')}
                      alt="Custom Furniture Crafting"
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"></div>
                    
                    {/* Floating badge */}
                    <div className="absolute bottom-6 left-6 bg-white/95 backdrop-blur-sm rounded-lg p-4 max-w-[250px] shadow-lg">
                      <div className="flex items-center mb-2">
                        <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                        <span className="text-sm font-medium text-gray-900">Premium Quality Guaranteed</span>
                      </div>
                      <p className="text-xs text-gray-600">Every piece comes with our lifetime craftsmanship warranty</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Step Progress Indicator */}
              <div className="max-w-4xl mx-auto mb-12 animate-fade-in" style={{ animationDelay: '0.3s' }}>
                <div className="flex items-center justify-between relative">
                  {/* Progress Line */}
                  <div className="absolute top-1/2 left-0 w-full h-0.5 bg-kgray-light transform -translate-y-1/2"></div>
                  <div
                    className="absolute top-1/2 left-0 h-0.5 bg-primary transform -translate-y-1/2 transition-all duration-500 ease-out"
                    style={{ width: `${((currentStep - 1) / (totalSteps - 1)) * 100}%` }}
                  ></div>

                  {/* Step Indicators */}
                  {[
                    { number: 1, title: 'Project Type', icon: '🎯' },
                    { number: 2, title: 'Your Vision', icon: '✨' },
                    { number: 3, title: 'Inspiration', icon: '🎨' },
                    { number: 4, title: 'Details', icon: '📋' }
                  ].map((step) => (
                    <div key={step.number} className="relative flex flex-col items-center">
                      <div
                        className={`w-12 h-12 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300 ${
                          currentStep >= step.number
                            ? 'bg-primary text-white shadow-lg scale-110'
                            : currentStep === step.number - 1
                            ? 'bg-primary/20 text-primary border-2 border-primary'
                            : 'bg-white text-kgray-medium border-2 border-kgray-light'
                        }`}
                      >
                        {currentStep > step.number ? (
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        ) : (
                          <span className="text-lg">{step.icon}</span>
                        )}
                      </div>
                      <span className={`mt-2 text-xs font-medium transition-colors duration-300 ${
                        currentStep >= step.number ? 'text-primary' : 'text-foreground/50'
                      }`}>
                        {step.title}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Main Form Container */}
              <div className="max-w-4xl mx-auto animate-fade-in" style={{ animationDelay: '0.4s' }}>
                <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/20 overflow-hidden">
                    {/* Step Content */}
                    <div className="min-h-[400px]">
                      {/* Step 1: Project Type */}
                      {currentStep === 1 && (
                        <div className="space-y-8 animate-fade-in">
                          <div className="text-center">
                            <h3 className="font-serif text-2xl font-medium text-foreground mb-3">
                              What would you like to create?
                            </h3>
                            <p className="text-foreground/70">
                              Choose the type of furniture piece you have in mind
                            </p>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {[
                              { id: 'seating', name: 'Seating & Sofas', icon: '🛋️', desc: 'Chairs, sofas, benches' },
                              { id: 'tables', name: 'Tables & Desks', icon: '🪑', desc: 'Dining, coffee, work tables' },
                              { id: 'storage', name: 'Storage Solutions', icon: '🗄️', desc: 'Cabinets, shelves, wardrobes' },
                              { id: 'bedroom', name: 'Bedroom Furniture', icon: '🛏️', desc: 'Beds, nightstands, dressers' },
                              { id: 'outdoor', name: 'Outdoor Pieces', icon: '🌿', desc: 'Patio, garden furniture' },
                              { id: 'other', name: 'Something Else', icon: '✨', desc: 'Custom unique pieces' }
                            ].map((category) => (
                              <button
                                key={category.id}
                                onClick={() => updateFormData('category', category.id)}
                                className={`p-6 rounded-2xl border-2 transition-all duration-300 text-left group hover:scale-105 ${
                                  formData.category === category.id
                                    ? 'border-primary bg-primary/5 shadow-lg'
                                    : 'border-kgray-light hover:border-primary/50 bg-white/50'
                                }`}
                              >
                                <div className="text-3xl mb-3">{category.icon}</div>
                                <h4 className="font-medium text-foreground mb-2 group-hover:text-primary transition-colors">
                                  {category.name}
                                </h4>
                                <p className="text-sm text-foreground/60">{category.desc}</p>
                              </button>
                            ))}
                          </div>

                          {formData.category && (
                            <div className="space-y-4 animate-fade-in">
                              <label className="block text-sm font-medium text-foreground">
                                Tell us more about your project
                              </label>
                              <textarea
                                value={formData.projectType}
                                onChange={(e) => updateFormData('projectType', e.target.value)}
                                rows={3}
                                className="w-full px-4 py-3 bg-white/70 border border-kgray-light rounded-xl text-foreground placeholder-foreground/50 focus:border-primary focus:ring-0 focus:bg-white transition-all duration-200 resize-none"
                                placeholder="Describe the specific piece you have in mind..."
                              />
                            </div>
                          )}
                        </div>
                      )}

                      {/* Step 2: Vision & Details */}
                      {currentStep === 2 && (
                        <div className="space-y-8 animate-fade-in">
                          <div className="text-center">
                            <h3 className="font-serif text-2xl font-medium text-foreground mb-3">
                              Share your vision
                            </h3>
                            <p className="text-foreground/70">
                              Help us understand your style and requirements
                            </p>
                          </div>

                          <div className="space-y-6">
                            {/* Description */}
                            <div>
                              <label className="block text-sm font-medium text-foreground mb-3">
                                Describe your vision in detail
                              </label>
                              <textarea
                                value={formData.description}
                                onChange={(e) => updateFormData('description', e.target.value)}
                                rows={4}
                                className="w-full px-4 py-3 bg-white/70 border border-kgray-light rounded-xl text-foreground placeholder-foreground/50 focus:border-primary focus:ring-0 focus:bg-white transition-all duration-200 resize-none"
                                placeholder="Tell us about your dream piece... What should it look like? How will you use it? Any specific features you want?"
                              />
                            </div>

                            {/* Style Preference */}
                            <div>
                              <label className="block text-sm font-medium text-foreground mb-3">
                                Style preference
                              </label>
                              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                                {[
                                  'Modern', 'Traditional', 'Minimalist', 'Industrial',
                                  'Scandinavian', 'Mid-Century', 'Rustic', 'Contemporary'
                                ].map((style) => (
                                  <button
                                    key={style}
                                    onClick={() => updateFormData('style', style)}
                                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                                      formData.style === style
                                        ? 'bg-primary text-white shadow-md'
                                        : 'bg-white/70 text-foreground border border-kgray-light hover:border-primary/50'
                                    }`}
                                  >
                                    {style}
                                  </button>
                                ))}
                              </div>
                            </div>

                            {/* Materials */}
                            <div>
                              <label className="block text-sm font-medium text-foreground mb-3">
                                Preferred materials (select all that apply)
                              </label>
                              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                                {[
                                  'Solid Wood', 'Metal', 'Leather', 'Fabric', 'Glass', 'Stone'
                                ].map((material) => (
                                  <button
                                    key={material}
                                    onClick={() => {
                                      const materials = formData.materials.includes(material)
                                        ? formData.materials.filter(m => m !== material)
                                        : [...formData.materials, material];
                                      updateFormData('materials', materials);
                                    }}
                                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                                      formData.materials.includes(material)
                                        ? 'bg-primary text-white shadow-md'
                                        : 'bg-white/70 text-foreground border border-kgray-light hover:border-primary/50'
                                    }`}
                                  >
                                    {material}
                                  </button>
                                ))}
                              </div>
                            </div>

                            {/* Dimensions */}
                            <div>
                              <label className="block text-sm font-medium text-foreground mb-3">
                                Approximate dimensions (if known)
                              </label>
                              <input
                                type="text"
                                value={formData.dimensions}
                                onChange={(e) => updateFormData('dimensions', e.target.value)}
                                className="w-full px-4 py-3 bg-white/70 border border-kgray-light rounded-xl text-foreground placeholder-foreground/50 focus:border-primary focus:ring-0 focus:bg-white transition-all duration-200"
                                placeholder="e.g., 6ft x 3ft x 2.5ft or 'Standard dining table size'"
                              />
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Step 3: Inspiration */}
                      {currentStep === 3 && (
                        <div className="space-y-8 animate-fade-in">
                          <div className="text-center">
                            <h3 className="font-serif text-2xl font-medium text-foreground mb-3">
                              Show us your inspiration
                            </h3>
                            <p className="text-foreground/70">
                              Upload images or share references that inspire your vision
                            </p>
                          </div>

                          <div className="space-y-6">
                            {/* File Upload */}
                            <div>
                              <label className="block text-sm font-medium text-foreground mb-3">
                                Upload inspiration images
                              </label>
                              <div className="border-2 border-dashed border-kgray-light rounded-2xl p-8 text-center bg-white/50 hover:border-primary/50 transition-colors duration-300 group">
                                <div className="space-y-4">
                                  <div className="mx-auto w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center group-hover:bg-primary/20 transition-colors duration-300">
                                    <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                  </div>
                                  <div>
                                    <label htmlFor="file-upload" className="cursor-pointer">
                                      <span className="text-primary font-semibold hover:text-primary-dark transition-colors">
                                        Click to upload
                                      </span>
                                      <span className="text-foreground/60"> or drag and drop</span>
                                      <input
                                        id="file-upload"
                                        type="file"
                                        className="sr-only"
                                        multiple
                                        accept="image/*"
                                        onChange={(e) => {
                                          if (e.target.files) {
                                            updateFormData('images', Array.from(e.target.files));
                                          }
                                        }}
                                      />
                                    </label>
                                  </div>
                                  <p className="text-sm text-foreground/50">PNG, JPG, GIF up to 10MB each</p>
                                </div>
                              </div>

                              {formData.images.length > 0 && (
                                <div className="mt-4">
                                  <p className="text-sm text-foreground/70 mb-2">
                                    {formData.images.length} file(s) selected
                                  </p>
                                  <div className="flex flex-wrap gap-2">
                                    {formData.images.map((file, index) => (
                                      <span key={index} className="px-3 py-1 bg-primary/10 text-primary text-xs rounded-full">
                                        {file.name}
                                      </span>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </div>

                            {/* Inspiration Notes */}
                            <div>
                              <label className="block text-sm font-medium text-foreground mb-3">
                                Additional inspiration notes
                              </label>
                              <textarea
                                value={formData.inspirationNotes}
                                onChange={(e) => updateFormData('inspirationNotes', e.target.value)}
                                rows={4}
                                className="w-full px-4 py-3 bg-white/70 border border-kgray-light rounded-xl text-foreground placeholder-foreground/50 focus:border-primary focus:ring-0 focus:bg-white transition-all duration-200 resize-none"
                                placeholder="Share any websites, Pinterest boards, or specific pieces that inspire you..."
                              />
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Step 4: Contact & Budget */}
                      {currentStep === 4 && (
                        <div className="space-y-8 animate-fade-in">
                          <div className="text-center">
                            <h3 className="font-serif text-2xl font-medium text-foreground mb-3">
                              Let's connect
                            </h3>
                            <p className="text-foreground/70">
                              Share your contact details and project timeline
                            </p>
                          </div>

                          <div className="space-y-6">
                            {/* Contact Information */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                              <div>
                                <label className="block text-sm font-medium text-foreground mb-2">
                                  Full Name *
                                </label>
                                <input
                                  type="text"
                                  value={formData.fullName}
                                  onChange={(e) => updateFormData('fullName', e.target.value)}
                                  className="w-full px-4 py-3 bg-white/70 border border-kgray-light rounded-xl text-foreground placeholder-foreground/50 focus:border-primary focus:ring-0 focus:bg-white transition-all duration-200"
                                  placeholder="Enter your full name"
                                  required
                                />
                              </div>

                              <div>
                                <label className="block text-sm font-medium text-foreground mb-2">
                                  Email Address *
                                </label>
                                <input
                                  type="email"
                                  value={formData.email}
                                  onChange={(e) => updateFormData('email', e.target.value)}
                                  className="w-full px-4 py-3 bg-white/70 border border-kgray-light rounded-xl text-foreground placeholder-foreground/50 focus:border-primary focus:ring-0 focus:bg-white transition-all duration-200"
                                  placeholder="<EMAIL>"
                                  required
                                />
                              </div>
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-foreground mb-2">
                                Phone Number
                              </label>
                              <input
                                type="tel"
                                value={formData.phone}
                                onChange={(e) => updateFormData('phone', e.target.value)}
                                className="w-full px-4 py-3 bg-white/70 border border-kgray-light rounded-xl text-foreground placeholder-foreground/50 focus:border-primary focus:ring-0 focus:bg-white transition-all duration-200"
                                placeholder="+****************"
                              />
                            </div>

                            {/* Budget */}
                            <div>
                              <label className="block text-sm font-medium text-foreground mb-3">
                                Investment Range *
                              </label>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                {[
                                  { value: 'under-1000', label: 'Under $1,000' },
                                  { value: '1000-2500', label: '$1,000 - $2,500' },
                                  { value: '2500-5000', label: '$2,500 - $5,000' },
                                  { value: '5000-10000', label: '$5,000 - $10,000' },
                                  { value: 'over-10000', label: '$10,000+' },
                                  { value: 'discuss', label: 'Prefer to discuss' }
                                ].map((option) => (
                                  <button
                                    key={option.value}
                                    onClick={() => updateFormData('budget', option.value)}
                                    className={`px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 text-left ${
                                      formData.budget === option.value
                                        ? 'bg-primary text-white shadow-md'
                                        : 'bg-white/70 text-foreground border border-kgray-light hover:border-primary/50'
                                    }`}
                                  >
                                    {option.label}
                                  </button>
                                ))}
                              </div>
                            </div>

                            {/* Timeline */}
                            <div>
                              <label className="block text-sm font-medium text-foreground mb-3">
                                Project Timeline
                              </label>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                {[
                                  { value: 'asap', label: 'As soon as possible' },
                                  { value: '1-2months', label: '1-2 months' },
                                  { value: '3-6months', label: '3-6 months' },
                                  { value: 'flexible', label: 'Flexible timeline' }
                                ].map((option) => (
                                  <button
                                    key={option.value}
                                    onClick={() => updateFormData('timeline', option.value)}
                                    className={`px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 text-left ${
                                      formData.timeline === option.value
                                        ? 'bg-primary text-white shadow-md'
                                        : 'bg-white/70 text-foreground border border-kgray-light hover:border-primary/50'
                                    }`}
                                  >
                                    {option.label}
                                  </button>
                                ))}
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Navigation Buttons */}
                    <div className="flex justify-between items-center pt-8 border-t border-kgray-light/50">
                      <button
                        onClick={prevStep}
                        disabled={currentStep === 1}
                        className={`flex items-center px-6 py-3 rounded-xl font-medium transition-all duration-300 ${
                          currentStep === 1
                            ? 'text-foreground/30 cursor-not-allowed'
                            : 'text-foreground hover:text-primary hover:bg-primary/5'
                        }`}
                      >
                        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                        </svg>
                        Previous
                      </button>

                      <div className="flex items-center space-x-2">
                        {Array.from({ length: totalSteps }, (_, i) => (
                          <div
                            key={i}
                            className={`w-2 h-2 rounded-full transition-all duration-300 ${
                              i + 1 <= currentStep ? 'bg-primary' : 'bg-kgray-light'
                            }`}
                          />
                        ))}
                      </div>

                      {currentStep < totalSteps ? (
                        <button
                          onClick={nextStep}
                          disabled={
                            (currentStep === 1 && !formData.category) ||
                            (currentStep === 2 && !formData.description) ||
                            (currentStep === 4 && (!formData.fullName || !formData.email || !formData.budget))
                          }
                          className="flex items-center px-6 py-3 bg-primary hover:bg-primary-dark text-white rounded-xl font-medium transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none shadow-lg hover:shadow-xl"
                        >
                          Next
                          <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </button>
                      ) : (
                        <button
                          onClick={() => {
                            // Handle form submission
                            console.log('Form submitted:', formData);
                            alert('Thank you! We\'ll be in touch within 24 hours to discuss your custom project.');
                          }}
                          disabled={!formData.fullName || !formData.email || !formData.budget}
                          className="flex items-center px-8 py-3 bg-primary hover:bg-primary-dark text-white rounded-xl font-medium transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none shadow-lg hover:shadow-xl"
                        >
                          <span className="mr-2">Submit Request</span>
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                          </svg>
                        </button>
                      )}
                    </div>
                  </div>
                </div>

                {/* Trust Indicators */}
                <div className="flex items-center justify-center mt-8 space-x-8 text-sm text-foreground/60">
                  <div className="flex items-center">
                    <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    Free Consultation
                  </div>
                  <div className="flex items-center">
                    <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    No Obligation
                  </div>
                  <div className="flex items-center">
                    <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    Expert Guidance
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
