'use client';

import { useState } from 'react';
import Image from "next/image";
import { getImageUrl } from "@/lib/placeholder-images";

export default function CustomDesignPage() {
  const [selectedCategory, setSelectedCategory] = useState('');
  const [isFormVisible, setIsFormVisible] = useState(false);

  const categories = [
    {
      id: 'living',
      title: 'Living Room',
      subtitle: 'Sofas, Chairs & Tables',
      image: getImageUrl('living-room'),
      color: 'from-blue-500 to-purple-600'
    },
    {
      id: 'bedroom',
      title: 'Bedroom',
      subtitle: 'Beds, Dressers & Nightstands',
      image: getImageUrl('bedroom'),
      color: 'from-green-500 to-teal-600'
    },
    {
      id: 'dining',
      title: 'Dining Room',
      subtitle: 'Tables, Chairs & Storage',
      image: getImageUrl('dining-room'),
      color: 'from-orange-500 to-red-600'
    },
    {
      id: 'office',
      title: 'Home Office',
      subtitle: '<PERSON><PERSON>, Chairs & Shelving',
      image: getImageUrl('office'),
      color: 'from-purple-500 to-pink-600'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100">
      {/* Hero Section with Split Layout */}
      <section className="relative min-h-screen flex items-center">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">

            {/* Left Side - Content */}
            <div className="space-y-8">
              <div className="space-y-6">
                <div className="inline-flex items-center px-4 py-2 bg-primary/10 rounded-full text-primary text-sm font-medium">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                  </svg>
                  Custom Furniture Design
                </div>

                <h1 className="text-5xl lg:text-7xl font-serif font-bold text-gray-900 leading-tight">
                  Design Your
                  <span className="block text-transparent bg-clip-text bg-gradient-to-r from-primary to-accent">
                    Dream Space
                  </span>
                </h1>

                <p className="text-xl text-gray-600 leading-relaxed max-w-lg">
                  Collaborate with our master craftsmen to bring your unique vision to life.
                  Every piece is handcrafted with precision and passion.
                </p>
              </div>

              {/* Interactive Stats */}
              <div className="grid grid-cols-3 gap-6">
                <div className="text-center p-4 bg-white rounded-2xl shadow-sm border border-gray-100">
                  <div className="text-2xl font-bold text-primary">500+</div>
                  <div className="text-sm text-gray-600">Custom Pieces</div>
                </div>
                <div className="text-center p-4 bg-white rounded-2xl shadow-sm border border-gray-100">
                  <div className="text-2xl font-bold text-primary">15+</div>
                  <div className="text-sm text-gray-600">Years Experience</div>
                </div>
                <div className="text-center p-4 bg-white rounded-2xl shadow-sm border border-gray-100">
                  <div className="text-2xl font-bold text-primary">98%</div>
                  <div className="text-sm text-gray-600">Satisfaction Rate</div>
                </div>
              </div>

              {/* CTA Button */}
              <button
                onClick={() => setIsFormVisible(true)}
                className="group relative px-8 py-4 bg-gradient-to-r from-primary to-primary-dark text-white rounded-2xl font-semibold text-lg shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
              >
                <span className="relative z-10">Start Your Design Journey</span>
                <div className="absolute inset-0 bg-gradient-to-r from-primary-dark to-primary rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <svg className="inline-block w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </button>
            </div>

            {/* Right Side - Visual */}
            <div className="relative">
              <div className="relative z-10">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-4">
                    <div className="h-48 bg-gradient-to-br from-primary/20 to-accent/20 rounded-3xl p-6 flex items-end">
                      <div className="text-white">
                        <div className="w-8 h-8 bg-white/20 rounded-full mb-2"></div>
                        <div className="text-sm font-medium">Modern Minimalist</div>
                      </div>
                    </div>
                    <div className="h-32 bg-gradient-to-br from-green-400/20 to-blue-400/20 rounded-3xl"></div>
                  </div>
                  <div className="space-y-4 pt-8">
                    <div className="h-32 bg-gradient-to-br from-orange-400/20 to-red-400/20 rounded-3xl"></div>
                    <div className="h-48 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-3xl p-6 flex items-end">
                      <div className="text-white">
                        <div className="w-8 h-8 bg-white/20 rounded-full mb-2"></div>
                        <div className="text-sm font-medium">Luxury Comfort</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Floating Elements */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full opacity-20 animate-pulse"></div>
              <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full opacity-20 animate-pulse"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Category Selection Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-serif font-bold text-gray-900 mb-4">
              Choose Your Space
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Select the room you'd like to transform and we'll guide you through the design process
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {categories.map((category, index) => (
              <div
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`group relative overflow-hidden rounded-3xl cursor-pointer transform transition-all duration-500 hover:scale-105 ${
                  selectedCategory === category.id ? 'ring-4 ring-primary shadow-2xl scale-105' : 'hover:shadow-xl'
                }`}
              >
                <div className="aspect-[4/5] relative">
                  <Image
                    src={category.image}
                    alt={category.title}
                    fill
                    className="object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className={`absolute inset-0 bg-gradient-to-t ${category.color} opacity-60 group-hover:opacity-70 transition-opacity duration-300`}></div>

                  <div className="absolute inset-0 p-6 flex flex-col justify-end text-white">
                    <h3 className="text-2xl font-bold mb-2">{category.title}</h3>
                    <p className="text-sm opacity-90">{category.subtitle}</p>

                    {selectedCategory === category.id && (
                      <div className="mt-4 flex items-center text-sm font-medium">
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        Selected
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {selectedCategory && (
            <div className="text-center mt-12 animate-fade-in">
              <button
                onClick={() => setIsFormVisible(true)}
                className="px-8 py-4 bg-primary hover:bg-primary-dark text-white rounded-2xl font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
              >
                Continue with {categories.find(c => c.id === selectedCategory)?.title}
              </button>
            </div>
          )}
        </div>
      </section>

      {/* Design Form Modal */}
      {isFormVisible && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-3xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-8">
              <div className="flex items-center justify-between mb-8">
                <div>
                  <h3 className="text-3xl font-serif font-bold text-gray-900">Let's Create Together</h3>
                  <p className="text-gray-600 mt-2">Tell us about your vision and we'll make it reality</p>
                </div>
                <button
                  onClick={() => setIsFormVisible(false)}
                  className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <form className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                    <input
                      type="text"
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent transition-all"
                      placeholder="Enter your full name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                    <input
                      type="email"
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent transition-all"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                  <input
                    type="tel"
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent transition-all"
                    placeholder="+****************"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Project Description</label>
                  <textarea
                    rows={4}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent transition-all resize-none"
                    placeholder="Describe your dream furniture piece in detail. Include dimensions, materials, style preferences, and any special requirements..."
                  ></textarea>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Budget Range</label>
                  <select className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent transition-all">
                    <option value="">Select your budget range</option>
                    <option value="1000-2500">$1,000 - $2,500</option>
                    <option value="2500-5000">$2,500 - $5,000</option>
                    <option value="5000-10000">$5,000 - $10,000</option>
                    <option value="10000+">$10,000+</option>
                  </select>
                </div>

                <div className="flex items-center space-x-3">
                  <input type="checkbox" id="newsletter" className="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary" />
                  <label htmlFor="newsletter" className="text-sm text-gray-600">
                    I'd like to receive updates about my project and design inspiration
                  </label>
                </div>

                <button
                  type="submit"
                  className="w-full bg-gradient-to-r from-primary to-primary-dark text-white py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300"
                >
                  Start My Custom Design Project
                </button>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}