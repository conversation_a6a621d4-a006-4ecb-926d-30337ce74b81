@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;1,400&family=Raleway:wght@300;400;500;600&display=swap');
@import "tailwindcss";

/* Light mode variables (default) */
:root {
  --background: #f8f5f2;
  --foreground: #2d2a2a;
  --primary: #a67c52;
  --primary-light: #d4b996;
  --primary-dark: #7d5a3c;
  --accent: #4a6fa5;
  --accent-light: #c2d8eb;
  --gray-light: #e8e4e0;
  --gray-medium: #c9c5c1;
  --white: #ffffff;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Dark mode variables - only applied when .dark class is present */
.dark {
  --background: #1e1c1a;
  --foreground: #f0ebe5;
  --primary: #c19a6b;
  --primary-light: #d4b996;
  --primary-dark: #8c6d45;
  --accent: #6d8cb8;
  --accent-light: #a3bbd8;
  --gray-light: #3a3633;
  --gray-medium: #2d2a27;
  --white: #2d2a2a;
}

@layer base {
  h1, h2, h3, h4, h5 {
    font-family: 'Playfair Display', serif;
  }

  body {
    font-family: 'Raleway', sans-serif;
    font-weight: 300;
    letter-spacing: 0.01em;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
}

/* Custom Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
  animation: fadeIn 0.8s ease forwards;
}

@keyframes pulseSubtle {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(217, 119, 6, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(217, 119, 6, 0);
  }
}

.animate-pulse-subtle {
  animation: pulseSubtle 2s infinite;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-light);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-light);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary);
}
