import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import FloatingContact from "@/components/FloatingContact";
import { ThemeProvider } from "@/context/ThemeContext";
import { themeScript } from "./theme-script";
import { headers } from "next/headers";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "K-Plush Furniture Store",
  description: "Quality furniture with custom design options",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Check if we're on an admin route using headers
  const headersList = await headers();
  const isAdminRoute = headersList.has('x-is-admin-route');

  return (
    <html lang="en">
      <head>
        <script dangerouslySetInnerHTML={{ __html: themeScript() }} />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen flex flex-col`}
      >
        <ThemeProvider>
          {!isAdminRoute && <Header />}
          <main className="flex-grow">
            {children}
          </main>
          {!isAdminRoute && <Footer />}
          {!isAdminRoute && <FloatingContact />}
        </ThemeProvider>
      </body>
    </html>
  );
}
