import Image from "next/image";
import Link from "next/link";
import { products } from "@/lib/products";
import ProductCard from "@/components/ProductCard";
import { getImageUrl } from "@/lib/placeholder-images";
import HeroSection from "@/components/HeroSection";

export default function Home() {
  // Get featured products (first 3)
  const featuredProducts = products.slice(0, 3);

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <HeroSection />

      {/* Quick Category Navigation */}
      <section className="py-16 bg-kgray-light dark:bg-kgray-medium relative overflow-hidden">
        {/* Decorative elements */}
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          <div className="absolute top-1/2 left-0 w-32 h-32 rounded-full bg-accent opacity-5 blur-2xl"></div>
          <div className="absolute bottom-0 right-1/4 w-40 h-40 rounded-full bg-primary opacity-5 blur-3xl"></div>
        </div>

        <div className="container mx-auto px-6 relative">
          <div className="text-center mb-12 animate-fade-in">
            <span className="inline-block px-3 py-1 rounded-full bg-primary/10 text-primary text-xs tracking-wider font-medium mb-4">
              Shop by Category
            </span>
            <h2 className="font-serif text-2xl md:text-3xl font-medium text-foreground mb-4">
              Find Your Perfect Furniture
            </h2>
            <p className="text-foreground/70 max-w-lg mx-auto">
              Browse our carefully curated collections designed for every space in your home
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                name: "Living Room",
                description: "Sofas, chairs, coffee tables & more",
                image: "https://images.unsplash.com/photo-1555041469-a586c61ea9bc?q=80&w=800&auto=format&fit=crop",
                href: "/shop?category=living-room",
                delay: "0.2s"
              },
              {
                name: "Dining",
                description: "Tables, chairs & dining sets",
                image: "https://images.unsplash.com/photo-1449247709967-d4461a6a6103?q=80&w=800&auto=format&fit=crop",
                href: "/shop?category=dining",
                delay: "0.4s"
              },
              {
                name: "Bedroom",
                description: "Beds, wardrobes & nightstands",
                image: "https://images.unsplash.com/photo-1505693416388-ac5ce068fe85?q=80&w=800&auto=format&fit=crop",
                href: "/shop?category=bedroom",
                delay: "0.6s"
              }
            ].map((category, index) => (
              <div
                key={index}
                className="group relative animate-fade-in"
                style={{ animationDelay: category.delay }}
              >
                <Link href={category.href} className="block">
                  <div className="relative overflow-hidden rounded-xl bg-white dark:bg-gray-700/80 shadow-sm hover:shadow-lg transition-all duration-500 transform hover:-translate-y-1">
                    {/* Image */}
                    <div className="relative h-48 overflow-hidden">
                      <Image
                        src={category.image}
                        alt={category.name}
                        fill
                        className="object-cover transition-transform duration-700 group-hover:scale-110"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>

                      {/* Overlay content */}
                      <div className="absolute bottom-4 left-4 right-4">
                        <h3 className="font-serif text-xl font-medium text-white mb-1">
                          {category.name}
                        </h3>
                        <p className="text-white/80 text-sm">
                          {category.description}
                        </p>
                      </div>

                      {/* Hover arrow */}
                      <div className="absolute top-4 right-4 w-8 h-8 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-2 group-hover:translate-x-0">
                        <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                      </div>
                    </div>

                    {/* Bottom section */}
                    <div className="p-4">
                      <div className="flex items-center justify-between">
                        <span className="text-primary font-medium text-sm">
                          Explore Collection
                        </span>
                        <div className="h-px flex-1 bg-primary/20 mx-3"></div>
                        <svg className="w-4 h-4 text-primary transform group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </Link>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Products */}
      <section className="py-20 bg-background relative overflow-hidden">
        {/* Decorative elements */}
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          <div className="absolute top-1/3 right-0 w-64 h-64 rounded-full bg-primary opacity-5 blur-3xl"></div>
          <div className="absolute bottom-0 left-1/4 w-48 h-48 rounded-full bg-accent-light opacity-5 blur-2xl"></div>
        </div>

        <div className="container mx-auto px-6 relative">
          {/* Section heading with distinctive styling */}
          <div className="max-w-xl mx-auto text-center mb-16 animate-fade-in">
            <span className="inline-block px-3 py-1 rounded-full bg-primary/10 text-primary text-xs tracking-wider font-medium mb-4">
              Curated Selection
            </span>

            <h2 className="font-serif text-3xl md:text-4xl font-medium text-foreground mb-4 relative inline-block">
              Featured Collection
              <span className="absolute -bottom-2 left-0 w-full h-1 bg-primary/20"></span>
            </h2>

            <p className="text-foreground/70 max-w-2xl mx-auto mt-4">
              Explore our handpicked selection of premium furniture pieces designed for comfort and style, each crafted with meticulous attention to detail.
            </p>
          </div>

          {/* Products grid with staggered animation */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
            {featuredProducts.map((product, index) => (
              <div
                key={product.id}
                className="animate-fade-in"
                style={{ animationDelay: `${0.3 + index * 0.2}s` }}
              >
                <ProductCard
                  id={product.id}
                  name={product.name}
                  price={product.price}
                  image={product.image}
                  category={product.category}
                />
              </div>
            ))}
          </div>

          {/* View all button with distinctive styling */}
          <div className="text-center mt-16 animate-fade-in" style={{ animationDelay: '0.9s' }}>
            <Link
              href="/shop"
              className="relative overflow-hidden group inline-block border border-primary text-primary hover:text-white px-10 py-3 rounded-md font-medium transition-colors duration-500"
            >
              <span className="relative z-10">View All Products</span>
              <span className="absolute inset-0 w-full h-full bg-primary transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></span>
              <span className="absolute -top-10 -right-10 w-20 h-20 rounded-full bg-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></span>
            </Link>
          </div>
        </div>
      </section>

      {/* Popular Products Showcase */}
      <section className="py-20 bg-background relative overflow-hidden">
        {/* Decorative elements */}
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          <div className="absolute top-1/4 right-1/4 w-72 h-72 rounded-full bg-accent opacity-5 blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-48 h-48 rounded-full bg-primary opacity-5 blur-2xl"></div>
        </div>

        <div className="container mx-auto px-6 relative">
          {/* Section heading */}
          <div className="flex flex-col md:flex-row md:items-end md:justify-between mb-12 animate-fade-in">
            <div className="mb-6 md:mb-0">
              <span className="inline-block px-3 py-1 rounded-full bg-accent/10 text-accent text-xs tracking-wider font-medium mb-4">
                Trending Now
              </span>
              <h2 className="font-serif text-3xl md:text-4xl font-medium text-foreground mb-4 relative">
                <span className="relative">
                  Popular Choices
                  <span className="absolute bottom-1 left-0 h-3 w-full bg-accent/20 -z-10 transform -skew-x-3"></span>
                </span>
              </h2>
              <p className="text-foreground/70 max-w-lg">
                Discover what our customers love most. These bestsellers combine style, comfort, and exceptional value.
              </p>
            </div>

            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm text-foreground/60">
                <svg className="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                </svg>
                <span>Customer Favorites</span>
              </div>
              <Link
                href="/shop"
                className="text-accent hover:text-accent-light font-medium text-sm transition-colors duration-300 flex items-center"
              >
                View All
                <svg className="ml-1 w-4 h-4 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </Link>
            </div>
          </div>

          {/* Popular products grid with enhanced styling */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {products.slice(1, 5).map((product, index) => (
              <div
                key={product.id}
                className="group relative animate-fade-in"
                style={{ animationDelay: `${0.2 + index * 0.1}s` }}
              >
                <div className="relative overflow-hidden rounded-xl bg-white dark:bg-gray-700/80 shadow-sm hover:shadow-lg transition-all duration-500 transform hover:-translate-y-2">
                  {/* Popularity badge */}
                  <div className="absolute top-3 left-3 z-10">
                    <div className="bg-red-500 text-white text-xs px-2 py-1 rounded-full flex items-center">
                      <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                      </svg>
                      Hot
                    </div>
                  </div>

                  {/* Product image */}
                  <Link href={`/shop/${product.id}`} className="block relative overflow-hidden">
                    <div className="relative h-48 w-full overflow-hidden">
                      <Image
                        src={product.image}
                        alt={product.name}
                        fill
                        className="object-cover object-center transition-transform duration-700 group-hover:scale-110"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    </div>

                    {/* Quick actions on hover */}
                    <div className="absolute bottom-3 left-3 right-3 flex justify-between items-center opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-y-4 group-hover:translate-y-0">
                      <button className="bg-white/90 hover:bg-white text-gray-800 p-2 rounded-full shadow-md transition-all duration-300 hover:scale-110">
                        <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      </button>
                      <button className="bg-primary hover:bg-primary-dark text-white p-2 rounded-full shadow-md transition-all duration-300 hover:scale-110">
                        <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                      </button>
                    </div>
                  </Link>

                  {/* Product info */}
                  <div className="p-4">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <h3 className="font-serif text-sm font-medium text-foreground group-hover:text-primary transition-colors duration-300 line-clamp-2">
                          <Link href={`/shop/${product.id}`}>
                            {product.name}
                          </Link>
                        </h3>
                        <p className="text-xs text-foreground/60 mt-1">{product.category}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-bold text-primary">${product.price.toFixed(2)}</p>
                      </div>
                    </div>

                    {/* Rating stars */}
                    <div className="flex items-center mb-3">
                      <div className="flex text-yellow-400">
                        {[...Array(5)].map((_, i) => (
                          <svg key={i} className="w-3 h-3 fill-current" viewBox="0 0 24 24">
                            <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                          </svg>
                        ))}
                      </div>
                      <span className="text-xs text-foreground/60 ml-2">(4.8)</span>
                    </div>

                    {/* Quick add to cart */}
                    <button className="w-full bg-transparent border border-primary text-primary hover:bg-primary hover:text-white py-2 rounded-md text-xs font-medium transition-all duration-300 transform hover:scale-105">
                      Quick Add
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Bottom stats */}
          <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-6 animate-fade-in" style={{ animationDelay: '0.8s' }}>
            {[
              { number: "500+", label: "Happy Customers" },
              { number: "50+", label: "Furniture Pieces" },
              { number: "3 Weeks", label: "Custom Delivery" },
              { number: "2 Years", label: "Warranty" }
            ].map((stat, index) => (
              <div key={index} className="text-center">
                <div className="font-serif text-2xl md:text-3xl font-bold text-primary mb-1">
                  {stat.number}
                </div>
                <div className="text-foreground/70 text-sm">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Custom Design Section */}
      <section className="py-20 bg-kgray-light dark:bg-kgray-medium relative overflow-hidden">
        {/* Decorative elements */}
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          <div className="absolute -top-20 -left-20 w-80 h-80 rounded-full bg-primary opacity-5 blur-3xl"></div>
          <div className="absolute bottom-10 right-10 w-40 h-40 rounded-full bg-accent opacity-5 blur-2xl"></div>
        </div>

        <div className="container mx-auto px-6 relative">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            {/* Left side - Image with decorative elements */}
            <div className="order-2 lg:order-1 animate-fade-in" style={{ animationDelay: '0.3s' }}>
              <div className="relative">
                {/* Main image with frame */}
                <div className="relative h-[500px] w-full rounded-2xl overflow-hidden">
                  <Image
                    src={getImageUrl('custom-design')}
                    alt="Custom Furniture Design"
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"></div>
                </div>

                {/* Decorative elements */}
                <div className="absolute -bottom-6 -left-6 w-full h-full border-2 border-primary/20 rounded-2xl"></div>
                <div className="absolute -top-6 -right-6 w-full h-full border border-primary/10 rounded-2xl"></div>

                {/* Floating badge */}
                <div className="absolute bottom-8 right-8 bg-white dark:bg-[#2d2a2a] shadow-lg rounded-lg p-4 max-w-[200px]">
                  <p className="text-primary font-serif text-sm">From concept to creation in as little as 3 weeks</p>
                </div>
              </div>
            </div>

            {/* Right side - Content */}
            <div className="order-1 lg:order-2 animate-fade-in" style={{ animationDelay: '0.6s' }}>
              <span className="inline-block px-3 py-1 rounded-full bg-primary/10 text-primary text-xs tracking-wider font-medium mb-6">
                Custom Craftsmanship
              </span>

              <h2 className="font-serif text-3xl md:text-4xl font-medium text-foreground mb-6 relative">
                <span className="relative">
                  Bring Your Vision
                  <span className="absolute bottom-1 left-0 h-3 w-full bg-primary/20 -z-10 transform -skew-x-3"></span>
                </span>
                <span className="block mt-1">to Life</span>
              </h2>

              <p className="text-foreground/70 mb-8 leading-relaxed">
                Have a specific design in mind? Our expert craftsmen can turn your vision into reality.
                Submit your design and we&apos;ll deliver a custom-made piece that perfectly fits your space and style.
              </p>

              <div className="space-y-6 mb-10">
                {[
                  { title: 'Professional Consultation', desc: 'One-on-one sessions with our design experts' },
                  { title: 'Premium Materials', desc: 'Handpicked, sustainable materials for lasting quality' },
                  { title: 'Rapid Delivery', desc: 'From concept to creation in as little as 3 weeks' }
                ].map((item, index) => (
                  <div key={index} className="flex items-start">
                    <div className="relative mr-4">
                      <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                        <svg className="h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                    </div>
                    <div>
                      <h3 className="font-medium text-foreground">{item.title}</h3>
                      <p className="text-foreground/60 text-sm">{item.desc}</p>
                    </div>
                  </div>
                ))}
              </div>

              <Link
                href="/custom-design"
                className="relative overflow-hidden group inline-flex items-center bg-primary hover:bg-primary-dark text-white px-8 py-3 rounded-md font-medium transition-colors duration-500"
              >
                <span className="relative z-10">Start Your Custom Project</span>
                <svg className="ml-2 h-5 w-5 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
                <span className="absolute inset-0 w-full h-full bg-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left opacity-20"></span>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20 bg-background relative overflow-hidden">
        {/* Decorative elements */}
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          <div className="absolute top-0 right-0 w-64 h-64 rounded-full bg-primary opacity-5 blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-64 h-64 rounded-full bg-accent-light opacity-5 blur-3xl"></div>
        </div>

        <div className="container mx-auto px-6 relative">
          {/* Section heading with distinctive styling */}
          <div className="max-w-xl mx-auto text-center mb-16 animate-fade-in">
            <span className="inline-block px-3 py-1 rounded-full bg-primary/10 text-primary text-xs tracking-wider font-medium mb-4">
              Client Experiences
            </span>

            <h2 className="font-serif text-3xl md:text-4xl font-medium text-foreground mb-4 relative inline-block">
              What Our Clients Say
              <span className="absolute -bottom-2 left-0 w-full h-1 bg-primary/20"></span>
            </h2>

            <p className="text-foreground/70 max-w-2xl mx-auto mt-4">
              Don&apos;t just take our word for it. Here&apos;s what our satisfied clients have to say about their K-Plush experience.
            </p>
          </div>

          {/* Testimonials with distinctive styling */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                name: "Sarah Johnson",
                role: "Interior Designer",
                quote: "The custom dining table K-Plush created for us is absolutely stunning. The craftsmanship is impeccable, and it fits perfectly in our space.",
                delay: "0.3s"
              },
              {
                name: "Michael Chen",
                role: "Homeowner",
                quote: "I was impressed by how quickly they delivered my custom sofa. The quality is exceptional, and the team was a pleasure to work with throughout the process.",
                delay: "0.5s"
              },
              {
                name: "Emily Rodriguez",
                role: "Architect",
                quote: "The attention to detail on our bookshelf is remarkable. K-Plush took our rough sketch and transformed it into a beautiful piece that we&apos;ll cherish for years.",
                delay: "0.7s"
              }
            ].map((testimonial, index) => (
              <div
                key={index}
                className="relative bg-white dark:bg-gray-700/80 p-8 rounded-xl shadow-sm border border-kgray-light dark:border-gray-600 animate-fade-in"
                style={{ animationDelay: testimonial.delay }}
              >
                {/* Quote mark */}
                <div className="absolute -top-5 -left-2 text-6xl text-primary/20 dark:text-primary-light/40 font-serif">&quot;</div>

                {/* Stars */}
                <div className="flex items-center mb-6 relative">
                  <div className="text-primary dark:text-primary-light">
                    {[...Array(5)].map((_, i) => (
                      <svg key={i} className="inline-block h-5 w-5 fill-current" viewBox="0 0 24 24">
                        <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                      </svg>
                    ))}
                  </div>
                </div>

                {/* Quote */}
                <p className="text-foreground/80 dark:text-white mb-6 relative z-10 leading-relaxed">
                  &quot;{testimonial.quote}&quot;
                </p>

                {/* Divider */}
                <div className="h-px w-16 bg-primary/30 dark:bg-primary/50 mb-6"></div>

                {/* Author */}
                <div className="flex items-center">
                  <div className="h-10 w-10 rounded-full bg-primary/10 dark:bg-primary/30 flex items-center justify-center text-primary dark:text-white font-medium">
                    {testimonial.name.charAt(0)}
                  </div>
                  <div className="ml-3">
                    <p className="font-medium text-foreground dark:text-white">{testimonial.name}</p>
                    <p className="text-foreground/60 dark:text-white/80 text-sm">{testimonial.role}</p>
                  </div>
                </div>


              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose K-plush Section */}
      <section className="py-20 bg-kgray-light dark:bg-kgray-medium relative overflow-hidden">
        {/* Decorative elements */}
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          <div className="absolute -top-20 -right-20 w-80 h-80 rounded-full bg-accent opacity-5 blur-3xl"></div>
          <div className="absolute bottom-20 left-20 w-60 h-60 rounded-full bg-primary opacity-5 blur-2xl"></div>
        </div>

        <div className="container mx-auto px-6 relative">
          {/* Section heading */}
          <div className="max-w-2xl mx-auto text-center mb-16 animate-fade-in">
            <span className="inline-block px-3 py-1 rounded-full bg-primary/10 text-primary text-xs tracking-wider font-medium mb-4">
              Why Choose K-Plush
            </span>
            <h2 className="font-serif text-3xl md:text-4xl font-medium text-foreground mb-4 relative inline-block">
              Crafted for Rwanda
              <span className="absolute -bottom-2 left-0 w-full h-1 bg-primary/20"></span>
            </h2>
            <p className="text-foreground/70 max-w-2xl mx-auto mt-4">
              We understand the unique needs of Rwandan homes and deliver exceptional furniture with local expertise and international quality standards.
            </p>
          </div>

          {/* Benefits grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: (
                  <svg className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                ),
                title: "Local Craftsmanship",
                description: "Handcrafted by skilled Rwandan artisans using traditional techniques combined with modern design",
                delay: "0.2s"
              },
              {
                icon: (
                  <svg className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                ),
                title: "Fast Delivery",
                description: "Quick delivery across Kigali and major cities in Rwanda. Custom pieces ready in 3-4 weeks",
                delay: "0.4s"
              },
              {
                icon: (
                  <svg className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                ),
                title: "Quality Guarantee",
                description: "Premium materials sourced locally and internationally with 2-year warranty on all products",
                delay: "0.6s"
              },
              {
                icon: (
                  <svg className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
                  </svg>
                ),
                title: "24/7 Support",
                description: "Dedicated customer service in Kinyarwanda, English, and French. WhatsApp support available",
                delay: "0.8s"
              }
            ].map((benefit, index) => (
              <div
                key={index}
                className="group text-center animate-fade-in"
                style={{ animationDelay: benefit.delay }}
              >
                <div className="relative mb-6">
                  {/* Icon container with elegant styling */}
                  <div className="relative mx-auto w-16 h-16 rounded-2xl bg-white dark:bg-gray-700/80 shadow-sm flex items-center justify-center group-hover:shadow-lg transition-all duration-500 transform group-hover:-translate-y-1">
                    <div className="text-primary group-hover:text-primary-dark transition-colors duration-300">
                      {benefit.icon}
                    </div>
                    {/* Decorative elements */}
                    <div className="absolute -inset-1 rounded-2xl bg-gradient-to-br from-primary/20 to-accent/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10"></div>
                  </div>
                </div>

                <h3 className="font-serif text-lg font-medium text-foreground mb-3 group-hover:text-primary transition-colors duration-300">
                  {benefit.title}
                </h3>
                <p className="text-foreground/70 text-sm leading-relaxed">
                  {benefit.description}
                </p>

                {/* Decorative line */}
                <div className="h-px w-0 bg-primary mx-auto mt-4 group-hover:w-12 transition-all duration-500"></div>
              </div>
            ))}
          </div>

          {/* Call to action */}
          <div className="text-center mt-16 animate-fade-in" style={{ animationDelay: '1s' }}>
            <div className="inline-flex items-center space-x-4 bg-white dark:bg-gray-700/80 rounded-full px-8 py-4 shadow-sm">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full bg-green-500 animate-pulse"></div>
                <span className="text-foreground/80 text-sm font-medium">Available in Kigali & Rwanda</span>
              </div>
              <div className="h-4 w-px bg-primary/20"></div>
              <Link
                href="/contact"
                className="text-primary hover:text-primary-dark font-medium text-sm transition-colors duration-300 flex items-center"
              >
                Contact Us
                <svg className="ml-1 w-4 h-4 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Newsletter Subscription Section */}
      <section className="py-20 bg-background relative overflow-hidden">
        {/* Decorative elements */}
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          <div className="absolute top-0 left-1/4 w-96 h-96 rounded-full bg-primary opacity-5 blur-3xl"></div>
          <div className="absolute bottom-0 right-0 w-64 h-64 rounded-full bg-accent-light opacity-5 blur-2xl"></div>
        </div>

        <div className="container mx-auto px-6 relative">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white dark:bg-gray-700/80 rounded-2xl shadow-sm border border-kgray-light dark:border-gray-600 overflow-hidden">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
                {/* Left side - Content */}
                <div className="p-8 lg:p-12 flex flex-col justify-center animate-fade-in">
                  <span className="inline-block px-3 py-1 rounded-full bg-primary/10 text-primary text-xs tracking-wider font-medium mb-4 w-fit">
                    Stay Updated
                  </span>

                  <h3 className="font-serif text-2xl md:text-3xl font-medium text-foreground mb-4">
                    <span className="relative">
                      Get the Latest
                      <span className="absolute bottom-1 left-0 h-2 w-full bg-primary/20 -z-10 transform -skew-x-3"></span>
                    </span>
                    <span className="block mt-1">Furniture Trends</span>
                  </h3>

                  <p className="text-foreground/70 mb-6 leading-relaxed">
                    Subscribe to our newsletter and be the first to know about new arrivals, exclusive designs, and special offers for Rwanda customers.
                  </p>

                  {/* Benefits list */}
                  <div className="space-y-3 mb-8">
                    {[
                      "New product launches & collections",
                      "Exclusive discounts & early access",
                      "Interior design tips & inspiration",
                      "Custom design project showcases"
                    ].map((benefit, index) => (
                      <div key={index} className="flex items-center">
                        <div className="w-5 h-5 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                          <svg className="w-3 h-3 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        </div>
                        <span className="text-foreground/80 text-sm">{benefit}</span>
                      </div>
                    ))}
                  </div>

                  {/* Newsletter form */}
                  <form className="space-y-4">
                    <div className="flex flex-col sm:flex-row gap-3">
                      <div className="flex-1">
                        <input
                          type="email"
                          placeholder="Enter your email address"
                          className="w-full px-4 py-3 rounded-lg border border-kgray-light dark:border-gray-600 bg-background dark:bg-gray-800 text-foreground placeholder-foreground/50 focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all duration-300"
                          required
                        />
                      </div>
                      <button
                        type="submit"
                        className="relative overflow-hidden group bg-primary hover:bg-primary-dark text-gray-900 px-6 py-3 rounded-lg font-medium transition-all duration-300 shadow-sm hover:shadow-md"
                      >
                        <span className="relative z-10 flex items-center">
                          Subscribe
                          <svg className="ml-2 w-4 h-4 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                          </svg>
                        </span>
                        <span className="absolute inset-0 w-full h-full bg-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left opacity-20"></span>
                      </button>
                    </div>
                    <p className="text-foreground/50 text-xs">
                      We respect your privacy. Unsubscribe at any time.
                    </p>
                  </form>
                </div>

                {/* Right side - Image */}
                <div className="relative h-64 lg:h-auto animate-fade-in" style={{ animationDelay: '0.3s' }}>
                  <div className="absolute inset-0">
                    <Image
                      src="https://images.unsplash.com/photo-1586023492125-27b2c045efd7?q=80&w=800&auto=format&fit=crop"
                      alt="Newsletter"
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-l from-primary/20 to-transparent"></div>
                  </div>

                  {/* Floating elements */}
                  <div className="absolute bottom-6 right-6 bg-white dark:bg-gray-800 rounded-lg p-3 shadow-lg max-w-[160px]">
                    <div className="flex items-center space-x-2 mb-2">
                      <div className="w-2 h-2 rounded-full bg-green-500"></div>
                      <span className="text-xs font-medium text-foreground">1,200+ Subscribers</span>
                    </div>
                    <p className="text-xs text-foreground/70">Join our growing community</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
