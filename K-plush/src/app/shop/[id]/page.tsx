import { getProductById } from "@/lib/products";
import Image from "next/image";
import Link from "next/link";
import { notFound } from "next/navigation";

// Mark the component as async to properly handle params
export default async function ProductDetailPage({ params }: { params: Promise<{ id: string }> }) {
  // Await the params before accessing its properties
  const { id } = await params;
  
  // Now we can safely use id
  const product = getProductById(id);

  if (!product) {
    notFound();
  }

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="mb-6">
        <Link href="/shop" className="text-amber-500 hover:text-amber-600 transition-colors flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          Back to Shop
        </Link>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {/* Product Images */}
        <div className="space-y-4">
          <div className="relative h-[400px] w-full rounded-lg overflow-hidden bg-gray-100">
            <Image
              src={product.image}
              alt={product.name}
              fill
              className="object-cover"
            />
          </div>

          <div className="grid grid-cols-3 gap-4">
            {product.images.slice(0, 3).map((image, index) => (
              <div key={index} className="relative h-24 rounded-md overflow-hidden bg-gray-100">
                <Image
                  src={image}
                  alt={`${product.name} - View ${index + 1}`}
                  fill
                  className="object-cover"
                />
              </div>
            ))}
          </div>
        </div>

        {/* Product Info */}
        <div>
          <h1 className="text-3xl font-bold text-gray-800 mb-2">{product.name}</h1>
          <p className="text-amber-500 font-medium text-xl mb-4">${product.price.toFixed(2)}</p>

          <div className="mb-6">
            <h2 className="text-lg font-medium text-gray-800 mb-2">Description</h2>
            <p className="text-gray-600">{product.description}</p>
          </div>

          <div className="mb-6">
            <h2 className="text-lg font-medium text-gray-800 mb-2">Features</h2>
            <ul className="list-disc list-inside space-y-1 text-gray-600">
              {product.features.map((feature, index) => (
                <li key={index}>{feature}</li>
              ))}
            </ul>
          </div>

          <div className="mb-6">
            <h2 className="text-lg font-medium text-gray-800 mb-2">Dimensions</h2>
            <p className="text-gray-600">
              W: {product.dimensions.width}cm × H: {product.dimensions.height}cm × D: {product.dimensions.depth}cm
            </p>
          </div>

          <div className="mb-8">
            <h2 className="text-lg font-medium text-gray-800 mb-2">Materials</h2>
            <div className="flex flex-wrap gap-2">
              {product.materials.map((material, index) => (
                <span key={index} className="bg-gray-100 px-3 py-1 rounded-full text-sm text-gray-700">
                  {material}
                </span>
              ))}
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4">
            <button className="bg-amber-500 text-white px-8 py-3 rounded-md font-medium hover:bg-amber-600 transition-colors flex-1">
              Add to Cart
            </button>
            <button className="border border-gray-300 text-gray-700 px-8 py-3 rounded-md font-medium hover:bg-gray-50 transition-colors">
              Customize
            </button>
          </div>

          <div className="mt-6 text-gray-600 text-sm">
            <p className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              {product.inStock ? 'In Stock' : 'Out of Stock'}
            </p>
          </div>
        </div>
      </div>

      {/* 3D Viewer Placeholder */}
      <div className="mt-16">
        <h2 className="text-2xl font-bold text-gray-800 mb-4">3D Product Visualization</h2>
        <div className="bg-gray-100 rounded-lg h-[400px] flex items-center justify-center">
          <p className="text-gray-500">3D viewer coming soon</p>
        </div>
      </div>
    </div>
  );
}
