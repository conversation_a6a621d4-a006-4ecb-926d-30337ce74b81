import { products, getAllCategories } from "@/lib/products";
import ProductCard from "@/components/ProductCard";
import Link from "next/link";

export default async function ShopPage({ 
  searchParams 
}: { 
  searchParams: Promise<{ category?: string }> 
}) {
  // Await the searchParams before using it
  const { category } = await searchParams;
  
  const categories = getAllCategories();
  const selectedCategory = category;

  // Filter products by category if selected
  const filteredProducts = selectedCategory
    ? products.filter(product => product.category.toLowerCase() === selectedCategory.toLowerCase())
    : products;

  return (
    <div className="container mx-auto px-4 py-12">
      <h1 className="text-3xl font-bold text-foreground mb-8">Shop Our Collection</h1>

      {/* Category Filter */}
      <div className="mb-8">
        <div className="flex flex-wrap gap-2">
          <Link
            href="/shop"
            className={`px-4 py-2 rounded-md text-sm font-medium ${
              !selectedCategory
                ? 'bg-primary text-white'
                : 'bg-kgray-light dark:bg-kgray-medium text-foreground hover:bg-gray-200 dark:hover:bg-gray-700'
            } transition-colors`}
          >
            All
          </Link>

          {categories.map((category) => (
            <Link
              key={category}
              href={`/shop?category=${category.toLowerCase()}`}
              className={`px-4 py-2 rounded-md text-sm font-medium ${
                selectedCategory?.toLowerCase() === category.toLowerCase()
                  ? 'bg-primary text-white'
                  : 'bg-kgray-light dark:bg-kgray-medium text-foreground hover:bg-gray-200 dark:hover:bg-gray-700'
              } transition-colors`}
            >
              {category}
            </Link>
          ))}
        </div>
      </div>

      {/* Products Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
        {filteredProducts.map((product) => (
          <ProductCard
            key={product.id}
            id={product.id}
            name={product.name}
            price={product.price}
            image={product.image}
            category={product.category}
          />
        ))}
      </div>

      {/* Empty State */}
      {filteredProducts.length === 0 && (
        <div className="text-center py-12">
          <h3 className="text-xl font-medium text-foreground mb-2">No products found</h3>
          <p className="text-foreground/70">
            Try selecting a different category or check back later for new arrivals.
          </p>
        </div>
      )}
    </div>
  );
}
