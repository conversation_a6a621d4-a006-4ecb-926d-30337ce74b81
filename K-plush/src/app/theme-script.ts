// Script to prevent flash of wrong theme
export function themeScript() {
  return `
    (function() {
      // Get stored theme or default to light
      const storedTheme = localStorage.getItem('theme');
      
      // Only apply dark mode if explicitly set to dark in localStorage
      if (storedTheme === 'dark') {
        document.documentElement.classList.add('dark');
      } else {
        // Ensure light mode by removing dark class
        document.documentElement.classList.remove('dark');
        // Set default theme to light if not set
        if (!storedTheme) {
          localStorage.setItem('theme', 'light');
        }
      }
    })()
  `;
}
