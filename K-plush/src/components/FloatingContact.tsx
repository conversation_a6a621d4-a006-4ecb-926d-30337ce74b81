'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function FloatingContact() {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="fixed bottom-6 right-6 z-50">
      {/* Expanded contact options */}
      {isExpanded && (
        <div className="mb-4 space-y-3 animate-fade-in">
          {/* WhatsApp */}
          <Link
            href="https://wa.me/250793898899"
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-full shadow-xl transition-all duration-300 transform hover:scale-105 group border-2 border-white/20"
          >
            <svg className="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 24 24">
              <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.595z"/>
            </svg>
            <div className="text-left">
              <div className="text-sm font-medium">WhatsApp</div>
              <div className="text-xs opacity-90">+250 793 898 899</div>
            </div>
          </Link>

          {/* Phone */}
          <Link
            href="tel:+250727193570"
            className="flex items-center bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-full shadow-xl transition-all duration-300 transform hover:scale-105 group border-2 border-white/20"
          >
            <svg className="w-5 h-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
            </svg>
            <div className="text-left">
              <div className="text-sm font-medium">Call Us</div>
              <div className="text-xs opacity-90">+250 727 193 570</div>
            </div>
          </Link>

          {/* Location */}
          <Link
            href="https://maps.google.com/?q=KG+11+Ave+Kimironko+Kigali+Rwanda"
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center bg-red-500 hover:bg-red-600 text-white px-4 py-3 rounded-full shadow-xl transition-all duration-300 transform hover:scale-105 group border-2 border-white/20"
          >
            <svg className="w-5 h-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <div className="text-left">
              <div className="text-sm font-medium">Visit Us</div>
              <div className="text-xs opacity-90">Kimironko, Kigali</div>
            </div>
          </Link>

          {/* Email */}
          <Link
            href="mailto:<EMAIL>"
            className="flex items-center bg-purple-500 hover:bg-purple-600 text-white px-4 py-3 rounded-full shadow-xl transition-all duration-300 transform hover:scale-105 group border-2 border-white/20"
          >
            <svg className="w-5 h-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            <div className="text-left">
              <div className="text-sm font-medium">Email</div>
              <div className="text-xs opacity-90"><EMAIL></div>
            </div>
          </Link>
        </div>
      )}

      {/* Main toggle button */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className={`relative w-14 h-14 rounded-full shadow-xl transition-all duration-300 transform hover:scale-110 ${
          isExpanded
            ? 'bg-red-500 hover:bg-red-600 rotate-45'
            : 'bg-gradient-to-br from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700'
        }`}
        aria-label="Contact options"
      >
        {/* Enhanced shadow for better visibility */}
        <div className="absolute inset-0 rounded-full shadow-lg bg-black/10"></div>

        {/* Icon */}
        <div className="relative z-10 flex items-center justify-center w-full h-full text-white">
          {isExpanded ? (
            <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2.5}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          ) : (
            <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          )}
        </div>

        {/* Enhanced pulse animation when not expanded */}
        {!isExpanded && (
          <div className="absolute inset-0 rounded-full bg-blue-400 animate-ping opacity-30"></div>
        )}

        {/* Notification badge with better contrast */}
        <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center border-2 border-white shadow-sm">
          <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
        </div>
      </button>

      {/* Enhanced tooltip when not expanded */}
      {!isExpanded && (
        <div className="absolute bottom-full right-0 mb-3 px-4 py-2 bg-gray-900 text-white text-sm rounded-lg opacity-0 hover:opacity-100 transition-opacity duration-300 whitespace-nowrap shadow-lg border border-gray-700 group">
          <span className="font-medium">Need Help? Contact Us!</span>
          <div className="absolute top-full right-6 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
        </div>
      )}
    </div>
  );
}
