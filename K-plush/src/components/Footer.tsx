import Link from 'next/link';
import Image from 'next/image';

export default function Footer() {
  return (
    <footer className="relative bg-background overflow-hidden">
      {/* Decorative elements */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute bottom-0 left-0 w-full h-full">
          <div className="absolute -bottom-20 -left-20 w-80 h-80 rounded-full bg-primary opacity-5 blur-3xl"></div>
          <div className="absolute -bottom-10 right-1/3 w-60 h-60 rounded-full bg-accent-light opacity-5 blur-3xl"></div>
        </div>
      </div>

      {/* Top decorative line */}
      <div className="h-px w-full bg-gradient-to-r from-transparent via-primary-light to-transparent opacity-30"></div>

      <div className="relative container mx-auto px-6 py-16">
        {/* Main footer content */}
        <div className="grid grid-cols-1 md:grid-cols-12 gap-12">
          {/* Brand column */}
          <div className="md:col-span-4 space-y-6">
            <Link href="/" className="inline-block group">
              <div className="flex items-center">
                <div className="relative overflow-hidden rounded-full">
                  <div className="absolute inset-0 bg-gradient-to-br from-primary-light to-primary rounded-full"></div>
                  <Image
                    src="/k-plush-logo.png"
                    alt="K-Plush Logo"
                    width={45}
                    height={45}
                    className="relative z-10 p-1"
                  />
                </div>
                <div className="ml-3">
                  <span className="font-serif text-xl font-medium tracking-wide text-foreground">K-Plush</span>
                  <div className="h-px w-0 bg-primary group-hover:w-full transition-all duration-500 mt-1"></div>
                </div>
              </div>
            </Link>

            <p className="text-foreground/70 font-sans text-sm leading-relaxed max-w-xs">
              Crafting exceptional furniture that transforms spaces into expressions of style, comfort, and personality. Every piece tells a story of craftsmanship and attention to detail.
            </p>

            <div className="pt-4">
              <h4 className="font-serif text-sm text-foreground/90 mb-3">Subscribe to our newsletter</h4>
              <div className="flex">
                <input
                  type="email"
                  placeholder="Your email"
                  className="bg-kgray-light dark:bg-kgray-medium px-4 py-2 rounded-l-md border-0 text-sm focus:ring-1 focus:ring-primary w-full max-w-[220px]"
                />
                <button className="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-r-md text-sm transition-colors duration-300">
                  Subscribe
                </button>
              </div>
            </div>
          </div>

          {/* Navigation columns */}
          <div className="md:col-span-2">
            <h3 className="font-serif text-foreground text-base mb-5 relative inline-block">
              Shop
              <span className="absolute -bottom-1 left-0 w-1/2 h-px bg-primary"></span>
            </h3>
            <ul className="space-y-3">
              {['All Products', 'Living Room', 'Bedroom', 'Dining'].map((item, index) => (
                <li key={index}>
                  <Link
                    href={`/shop${item !== 'All Products' ? `?category=${item.toLowerCase().replace(' ', '-')}` : ''}`}
                    className="text-foreground/70 hover:text-primary transition-colors duration-300 text-sm inline-block relative group"
                  >
                    <span>{item}</span>
                    <span className="absolute left-0 bottom-0 w-0 h-px bg-primary transition-all duration-300 group-hover:w-full"></span>
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div className="md:col-span-2">
            <h3 className="font-serif text-foreground text-base mb-5 relative inline-block">
              Company
              <span className="absolute -bottom-1 left-0 w-1/2 h-px bg-primary"></span>
            </h3>
            <ul className="space-y-3">
              {['About Us', 'Custom Design', 'Contact'].map((item, index) => (
                <li key={index}>
                  <Link
                    href={`/${item.toLowerCase().replace(' ', '-')}`}
                    className="text-foreground/70 hover:text-primary transition-colors duration-300 text-sm inline-block relative group"
                  >
                    <span>{item}</span>
                    <span className="absolute left-0 bottom-0 w-0 h-px bg-primary transition-all duration-300 group-hover:w-full"></span>
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div className="md:col-span-4">
            <h3 className="font-serif text-foreground text-base mb-5 relative inline-block">
              Contact
              <span className="absolute -bottom-1 left-0 w-1/2 h-px bg-primary"></span>
            </h3>
            <ul className="space-y-4">
              <li className="flex items-start">
                <div className="mt-1">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h4 className="text-foreground text-sm font-medium">Visit Our Showroom</h4>
                  <p className="text-foreground/70 text-sm mt-1">123 Furniture Street, Design City</p>
                </div>
              </li>
              <li className="flex items-start">
                <div className="mt-1">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h4 className="text-foreground text-sm font-medium">Email Us</h4>
                  <p className="text-foreground/70 text-sm mt-1"><EMAIL></p>
                </div>
              </li>
              <li className="flex items-start">
                <div className="mt-1">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h4 className="text-foreground text-sm font-medium">Call Us</h4>
                  <p className="text-foreground/70 text-sm mt-1">+1 (555) 123-4567</p>
                </div>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom section */}
        <div className="mt-16 pt-8 border-t border-kgray-light dark:border-kgray-medium">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-foreground/60 text-sm">
              &copy; {new Date().getFullYear()} K-Plush Group Ltd. All rights reserved.
            </p>

            <div className="flex space-x-6 mt-6 md:mt-0">
              {['Facebook', 'Instagram', 'Twitter'].map((platform, index) => (
                <a
                  key={index}
                  href="#"
                  className="group relative"
                  aria-label={platform}
                >
                  <span className="absolute inset-0 rounded-full bg-primary/10 opacity-0 transform scale-0 group-hover:scale-100 group-hover:opacity-100 transition-all duration-300"></span>
                  <svg
                    className="h-5 w-5 text-foreground/60 group-hover:text-primary transition-colors duration-300 relative z-10"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                    aria-hidden="true"
                  >
                    {platform === 'Facebook' && (
                      <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd" />
                    )}
                    {platform === 'Instagram' && (
                      <path fillRule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clipRule="evenodd" />
                    )}
                    {platform === 'Twitter' && (
                      <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                    )}
                  </svg>
                </a>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
