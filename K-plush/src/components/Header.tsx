'use client';

import Link from 'next/link';
import Image from 'next/image';
import MobileMenu from './MobileMenu';
import ThemeToggle from './ThemeToggle';

export default function Header() {
  return (
    <header className="relative z-40">
      {/* Decorative elements */}
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
        <div className="absolute -top-10 -left-10 w-40 h-40 rounded-full bg-primary-light opacity-10 blur-3xl"></div>
        <div className="absolute top-20 right-1/4 w-20 h-20 rounded-full bg-accent-light opacity-10 blur-2xl"></div>
      </div>

      <div className="relative bg-background py-6">
        <div className="container mx-auto px-6">
          <div className="flex justify-between items-center">
            {/* Logo with distinctive styling */}
            <Link href="/" className="group relative">
              <div className="flex items-center">
                <div className="relative overflow-hidden rounded-full">
                  <div className="absolute inset-0 bg-gradient-to-br from-primary-light to-primary rounded-full transform -rotate-12 group-hover:rotate-12 transition-transform duration-700"></div>
                  <Image
                    src="/k-plush-logo.png"
                    alt="K-Plush Logo"
                    width={50}
                    height={50}
                    className="relative z-10 p-1 transition-transform duration-500 group-hover:scale-110"
                  />
                </div>
                <div className="ml-4">
                  <div className="relative overflow-hidden">
                    <h1 className="font-serif text-2xl font-medium tracking-wide text-foreground transform transition-transform duration-500 group-hover:-translate-y-full">K-Plush</h1>
                    <h1 className="font-serif text-2xl font-medium tracking-wide text-primary absolute top-0 transform translate-y-full transition-transform duration-500 group-hover:translate-y-0">K-Plush</h1>
                  </div>
                  <div className="h-px w-0 bg-primary group-hover:w-full transition-all duration-500 mt-1"></div>
                  <span className="text-[10px] uppercase tracking-[0.2em] text-primary-light">Quality in Every Detail</span>
                </div>
              </div>
            </Link>

            {/* Desktop Navigation - Distinctive and elegant */}
            <nav className="hidden lg:block">
              <ul className="flex items-center space-x-1">
                {[
                  { name: 'Home', path: '/' },
                  { name: 'Shop', path: '/shop' },
                  { name: 'Custom Design', path: '/custom-design' },
                  { name: 'About', path: '/about' },
                  { name: 'Contact', path: '/contact' }
                ].map((item, index) => (
                  <li key={index}>
                    <Link
                      href={item.path}
                      className="relative px-5 py-2 font-sans text-sm tracking-wider text-foreground hover:text-primary transition-colors duration-300 group"
                    >
                      <span className="relative z-10">{item.name}</span>
                      <span className="absolute bottom-0 left-0 w-0 h-[1px] bg-primary transition-all duration-300 group-hover:w-full"></span>
                      <span className="absolute inset-0 bg-primary-light opacity-0 group-hover:opacity-5 rounded-md transition-opacity duration-300"></span>
                    </Link>
                  </li>
                ))}
              </ul>
            </nav>

            {/* Right side actions with distinctive styling */}
            <div className="flex items-center">
              {/* Theme Toggle */}
              <ThemeToggle />

              <Link
                href="/cart"
                className="relative px-3 py-2 text-foreground hover:text-primary transition-colors duration-300 group"
                aria-label="Shopping cart"
              >
                <span className="absolute inset-0 bg-primary-light opacity-0 group-hover:opacity-10 rounded-full transition-opacity duration-300"></span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 relative z-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <span className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center rounded-full bg-primary text-white text-xs">2</span>
              </Link>

              <button
                className="relative px-3 py-2 text-foreground hover:text-primary transition-colors duration-300 group"
                aria-label="Search"
              >
                <span className="absolute inset-0 bg-primary-light opacity-0 group-hover:opacity-10 rounded-full transition-opacity duration-300"></span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 relative z-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </button>

              {/* Mobile menu component */}
              <MobileMenu />
            </div>
          </div>
        </div>
      </div>

      {/* Subtle decorative line */}
      <div className="h-px w-full bg-gradient-to-r from-transparent via-primary-light to-transparent opacity-30"></div>
    </header>
  );
}
