import Image from 'next/image';
import Link from 'next/link';

export default function HeroSection() {
  return (
    <section className="relative min-h-[90vh] overflow-hidden bg-background">
      {/* Background decorative elements */}

      {/* Decorative elements */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 rounded-full bg-primary opacity-5 blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 rounded-full bg-accent-light opacity-5 blur-3xl"></div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-6 h-full">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 h-full items-center py-20">
          {/* Left side - Text content */}
          <div className="order-2 lg:order-1 animate-fade-in" style={{ animationDelay: '0.3s' }}>
            <div className="relative">
              <span className="inline-block px-4 py-1 rounded-full bg-primary/10 text-primary text-xs tracking-wider font-medium mb-6">
                Craftsmanship Redefined
              </span>

              <h1 className="font-serif text-4xl md:text-5xl lg:text-6xl font-medium leading-tight text-foreground mb-6">
                <span className="block relative">
                  <span className="relative z-10">Furniture that</span>
                  <span className="absolute bottom-1 left-0 h-3 w-full bg-primary/20 -z-10 transform -skew-x-3"></span>
                </span>
                <span className="block mt-1">Tells Your Story</span>
              </h1>

              <p className="text-foreground/70 text-lg max-w-lg mb-8 leading-relaxed">
                Discover our collection of handcrafted furniture or bring your own design to life with our custom creation service.
              </p>

              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href="/shop"
                  className="relative overflow-hidden group bg-primary hover:bg-primary-dark text-gray-900 px-8 py-3 rounded-md font-medium transition-all duration-300 shadow-lg hover:shadow-xl"
                >
                  <span className="relative z-10 flex items-center">
                    <span className="text-base font-bold tracking-wide">Shop Collection</span>
                    <svg className="ml-2 h-5 w-5 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </span>
                  <span className="absolute inset-0 w-full h-full bg-white transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left opacity-20"></span>
                </Link>

                <Link
                  href="/custom-design"
                  className="relative overflow-hidden group bg-gray-800 border border-primary text-white px-8 py-3 rounded-md font-medium transition-all duration-300 shadow-lg hover:shadow-xl"
                >
                  <span className="relative z-10 flex items-center">
                    <span className="text-base font-bold tracking-wide">Custom Design</span>
                    <svg className="ml-2 h-5 w-5 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                    </svg>
                  </span>
                  <span className="absolute inset-0 w-full h-full bg-primary transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></span>
                </Link>
              </div>


            </div>
          </div>

          {/* Right side - Image */}
          <div className="order-1 lg:order-2 relative animate-fade-in" style={{ animationDelay: '0.6s' }}>
            <div className="relative h-[400px] md:h-[500px] lg:h-[600px] w-full">
              {/* Main image */}
              <div className="absolute inset-0 rounded-2xl overflow-hidden">
                <Image
                  src="https://images.unsplash.com/photo-1555041469-a586c61ea9bc?q=80&w=1000&auto=format&fit=crop"
                  alt="K-Plush Furniture"
                  fill
                  className="object-cover"
                  priority
                />
                <div className="absolute inset-0 bg-gradient-to-r from-primary/30 to-transparent mix-blend-multiply"></div>
              </div>

              {/* Floating elements */}
              <div className="absolute -bottom-6 -left-6 w-48 h-48 rounded-xl overflow-hidden shadow-xl transform rotate-6 animate-fade-in" style={{ animationDelay: '1s' }}>
                <Image
                  src="https://images.unsplash.com/photo-1493663284031-b7e3aefcae8e?q=80&w=1000&auto=format&fit=crop"
                  alt="K-Plush Detail"
                  fill
                  className="object-cover"
                />
              </div>

              <div className="absolute -top-4 -right-4 w-32 h-32 rounded-full overflow-hidden shadow-xl border border-gray-800 dark:border-gray-700 animate-fade-in" style={{ animationDelay: '1.2s' }}>
                <Image
                  src="https://images.unsplash.com/photo-1581539250439-c96689b516dd?q=80&w=1000&auto=format&fit=crop"
                  alt="K-Plush Workshop"
                  fill
                  className="object-cover"
                />
              </div>

              {/* Decorative elements */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-full border-2 border-primary/20 rounded-2xl -rotate-3"></div>
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-full border border-primary/10 rounded-2xl rotate-6"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom decorative line */}
      <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary-light to-transparent opacity-30"></div>
    </section>
  );
}
