'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

export default function MobileMenu() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  // Close menu when clicking outside or pressing escape
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setIsMenuOpen(false);
      }
    };

    const handleResize = () => {
      // Close menu on large screens
      if (window.innerWidth >= 1024) {
        setIsMenuOpen(false);
      }
    };

    if (isMenuOpen) {
      document.addEventListener('keydown', handleEscape);
      window.addEventListener('resize', handleResize);
      // Prevent body scroll when menu is open
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      window.removeEventListener('resize', handleResize);
      document.body.style.overflow = 'unset';
    };
  }, [isMenuOpen]);

  const menuItems = [
    { name: 'Home', path: '/', icon: 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6' },
    { name: 'Shop', path: '/shop', icon: 'M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z' },
    { name: 'Custom Design', path: '/custom-design', icon: 'M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4 4 4 0 004-4V5z' },
    { name: 'About', path: '/about', icon: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z' },
    { name: 'Contact', path: '/contact', icon: 'M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z' }
  ];

  const handleLinkClick = () => {
    setIsMenuOpen(false);
  };

  return (
    <div className="lg:hidden">
      {/* Mobile menu button */}
      <button 
        className="relative p-2 text-gray-700 dark:text-gray-200 hover:text-primary hover:bg-primary/10 dark:hover:bg-primary/20 transition-all duration-300 group rounded-lg"
        onClick={() => setIsMenuOpen(!isMenuOpen)}
        aria-label={isMenuOpen ? "Close menu" : "Open menu"}
        aria-expanded={isMenuOpen}
      >
        <span className="absolute inset-0 bg-primary-light opacity-0 group-hover:opacity-20 rounded-lg transition-opacity duration-300"></span>
        <div className="relative z-10">
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className="h-6 w-6 transition-transform duration-300" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d={isMenuOpen ? "M6 18L18 6M6 6l12 12" : "M4 6h16M4 12h16M4 18h16"} 
            />
          </svg>
        </div>
      </button>

      {/* Mobile Navigation Overlay */}
      <div 
        className={`fixed inset-0 bg-black/60 backdrop-blur-sm z-[60] transition-all duration-300 ${
          isMenuOpen ? 'opacity-100 visible' : 'opacity-0 invisible'
        }`}
        onClick={() => setIsMenuOpen(false)}
        aria-hidden="true"
      />
      
      {/* Mobile Navigation Menu */}
      <div 
        className={`fixed top-0 right-0 h-full w-[320px] bg-white dark:bg-gray-800 z-[70] shadow-2xl transform transition-all duration-300 ease-in-out ${
          isMenuOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
        role="dialog"
        aria-modal="true"
        aria-labelledby="mobile-menu-title"
      >
        {/* Decorative background elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 right-8 w-32 h-32 rounded-full bg-primary opacity-5 blur-3xl"></div>
          <div className="absolute bottom-32 left-8 w-24 h-24 rounded-full bg-accent-light opacity-5 blur-2xl"></div>
        </div>

        <div className="relative flex flex-col h-full">
          {/* Header with decorative line */}
          <div className="relative">
            <div className="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-600">
              <div className="flex items-center">
                <div className="relative">
                  <h2 id="mobile-menu-title" className="font-serif text-xl font-medium text-gray-900 dark:text-white">
                    Menu
                  </h2>
                  <div className="absolute -bottom-1 left-0 w-8 h-px bg-primary"></div>
                </div>
              </div>
              <button 
                className="p-2 text-gray-700 dark:text-gray-300 hover:text-primary transition-colors duration-300 group relative"
                onClick={() => setIsMenuOpen(false)}
                aria-label="Close menu"
              >
                <span className="absolute inset-0 bg-primary-light opacity-0 group-hover:opacity-10 rounded-full transition-opacity duration-300"></span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 relative z-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            {/* Decorative gradient line */}
            <div className="h-px w-full bg-gradient-to-r from-transparent via-primary-light to-transparent opacity-30"></div>
          </div>
          
          {/* Navigation with elegant styling */}
          <nav className="flex-1 p-6" role="navigation">
            <ul className="space-y-1 mt-4">
              {menuItems.map((item, index) => (
                <li key={item.name} className="animate-fade-in" style={{ animationDelay: `${index * 0.1}s` }}>
                  <Link 
                    href={item.path}
                    className="group relative block py-4 px-4 rounded-xl text-gray-800 dark:text-gray-100 hover:text-primary transition-all duration-300 overflow-hidden"
                    onClick={handleLinkClick}
                  >
                    {/* Hover background effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
                    
                    <span className="relative flex items-center">
                      {/* Icon */}
                      <div className="flex items-center justify-center w-8 h-8 mr-4 rounded-lg bg-primary/10 group-hover:bg-primary/20 transition-colors duration-300">
                        <svg 
                          className="h-4 w-4 text-primary" 
                          fill="none" 
                          viewBox="0 0 24 24" 
                          stroke="currentColor"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d={item.icon} />
                        </svg>
                      </div>
                      
                      {/* Text */}
                      <span className="font-medium text-base tracking-wide flex-1">{item.name}</span>
                      
                      {/* Arrow */}
                      <svg 
                        className="h-4 w-4 opacity-0 group-hover:opacity-100 transform translate-x-0 group-hover:translate-x-1 transition-all duration-300 text-primary ml-2" 
                        fill="none" 
                        viewBox="0 0 24 24" 
                        stroke="currentColor"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </span>
                  </Link>
                </li>
              ))}
            </ul>
          </nav>

          {/* Elegant footer */}
          <div className="relative">
            {/* Decorative gradient line */}
            <div className="h-px w-full bg-gradient-to-r from-transparent via-primary-light to-transparent opacity-30"></div>
            <div className="p-6 border-t border-gray-200 dark:border-gray-600">
              <div className="flex flex-col items-center space-y-3">
                <div className="flex items-center space-x-2">
                  <div className="w-6 h-6 rounded-full bg-gradient-to-br from-primary-light to-primary flex items-center justify-center">
                    <span className="text-white text-xs font-serif font-medium">K</span>
                  </div>
                  <span className="font-serif text-sm font-medium text-gray-700 dark:text-gray-300">K-Plush</span>
                </div>
                <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                  <span>Quality in Every Detail</span>
                  <span>&copy; 2024</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
