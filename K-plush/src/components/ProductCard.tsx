import Image from 'next/image';
import Link from 'next/link';

interface ProductCardProps {
  id: string;
  name: string;
  price: number;
  image: string;
  category: string;
}

export default function ProductCard({ id, name, price, image, category }: ProductCardProps) {
  return (
    <div className="group relative">
      {/* Card container with subtle hover effects */}
      <div className="relative overflow-hidden rounded-lg bg-kgray-light dark:bg-kgray-medium transition-all duration-500 transform group-hover:shadow-lg">
        {/* Image container with hover zoom effect */}
        <Link href={`/shop/${id}`} className="block relative overflow-hidden">
          <div className="relative h-72 w-full overflow-hidden">
            {/* Main product image */}
            <Image
              src={image}
              alt={name}
              fill
              className="object-cover object-center transition-transform duration-700 group-hover:scale-110"
            />

            {/* Overlay gradient on hover */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          </div>

          {/* Quick view button that appears on hover */}
          <div className="absolute bottom-4 left-0 w-full flex justify-center">
            <span className="bg-primary/90 dark:bg-[#2d2a2a] text-white px-4 py-2 rounded-full text-xs font-medium transform translate-y-10 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-500 shadow-md">
              Quick View
            </span>
          </div>

          {/* Category tag */}
          <div className="absolute top-4 left-4">
            <span className="bg-primary/10 text-primary text-xs px-3 py-1 rounded-full">
              {category}
            </span>
          </div>
        </Link>

        {/* Product info with elegant styling */}
        <div className="p-5">
          <div className="flex justify-between items-start mb-2">
            <h3 className="font-serif text-foreground group-hover:text-primary transition-colors duration-300">
              <Link href={`/shop/${id}`} className="block">
                {name}
              </Link>
            </h3>
            <p className="font-medium text-primary">${price.toFixed(2)}</p>
          </div>

          {/* Decorative line */}
          <div className="h-px w-0 bg-primary group-hover:w-full transition-all duration-500 mb-4"></div>

          {/* Add to cart button with unique hover effect */}
          <button className="relative overflow-hidden w-full bg-transparent border border-primary text-primary hover:text-white py-2 rounded-md text-sm font-medium transition-colors duration-500 group-hover:bg-primary">
            <span className="relative z-10">Add to Cart</span>
            <span className="absolute inset-0 w-full h-full bg-primary transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></span>
          </button>
        </div>
      </div>


    </div>
  );
}
