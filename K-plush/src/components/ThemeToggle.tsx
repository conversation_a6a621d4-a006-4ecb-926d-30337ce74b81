'use client';

import { useTheme } from '@/context/ThemeContext';
import { useEffect, useState } from 'react';

export default function ThemeToggle() {
  const { theme, toggleTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [currentTheme, setCurrentTheme] = useState<'light' | 'dark'>('light');

  // Avoid hydration mismatch by only rendering after mount
  useEffect(() => {
    setMounted(true);
    // Check if dark mode is active by checking the class on html element
    const isDarkMode = document.documentElement.classList.contains('dark');
    setCurrentTheme(isDarkMode ? 'dark' : 'light');
  }, []);

  // Update local state when theme changes
  useEffect(() => {
    if (mounted) {
      setCurrentTheme(theme);
    }
  }, [theme, mounted]);

  // Handle theme toggle with visual feedback
  const handleToggle = () => {
    toggleTheme();
    // Force immediate visual feedback
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    setCurrentTheme(newTheme);
    console.log('Theme toggled to:', newTheme, 'Dark class:', document.documentElement.classList.contains('dark'));
  };

  if (!mounted) return null;

  return (
    <button
      onClick={handleToggle}
      className="relative px-3 py-2 text-foreground hover:text-primary transition-colors duration-300 group"
      aria-label={`Switch to ${currentTheme === 'light' ? 'dark' : 'light'} mode`}
    >
      <span className="absolute inset-0 bg-primary-light opacity-0 group-hover:opacity-10 rounded-full transition-opacity duration-300"></span>
      <div className="relative w-10 h-5 bg-kgray-light dark:bg-kgray-medium rounded-full overflow-hidden transition-colors duration-300">
        {/* Track */}
        <div className="absolute inset-0 flex items-center justify-between px-1">
          {/* Sun icon */}
          <svg
            className="h-3 w-3 text-primary/70"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
            />
          </svg>

          {/* Moon icon */}
          <svg
            className="h-3 w-3 text-primary/70"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
            />
          </svg>
        </div>

        {/* Thumb/Handle */}
        <div
          className={`absolute top-0.5 left-0.5 w-4 h-4 rounded-full bg-white shadow-md transform transition-transform duration-300 ease-in-out ${
            currentTheme === 'dark' ? 'translate-x-5' : ''
          }`}
        >
          {/* Decorative elements */}
          <div className="absolute inset-0 rounded-full overflow-hidden">
            <div className="absolute inset-0 opacity-20">
              {currentTheme === 'light' ? (
                // Sun rays pattern
                <div className="absolute inset-0 bg-primary/20 rounded-full">
                  <div className="absolute inset-0 bg-gradient-to-tr from-yellow-200 to-primary-light"></div>
                </div>
              ) : (
                // Moon crater pattern
                <div className="absolute inset-0 bg-primary/20 rounded-full">
                  <div className="absolute inset-0 bg-gradient-to-br from-gray-600 to-gray-900"></div>
                  <div className="absolute top-1 right-1.5 w-1 h-1 rounded-full bg-gray-700/50"></div>
                  <div className="absolute bottom-1.5 left-1 w-0.5 h-0.5 rounded-full bg-gray-700/50"></div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </button>
  );
}
