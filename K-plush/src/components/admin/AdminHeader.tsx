'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  MagnifyingGlassIcon,
  BellIcon,
  UserCircleIcon,
  Bars3Icon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline';

export default function AdminHeader() {
  const [searchQuery, setSearchQuery] = useState('');
  const [showUserMenu, setShowUserMenu] = useState(false);
  const router = useRouter();

  const handleLogout = () => {
    localStorage.removeItem('admin_token');
    router.push('/admin/login');
  };

  return (
    <header className="bg-white dark:bg-kgray-dark">
      <div className="flex items-center justify-between px-6 py-4">
        {/* Mobile menu button */}
        <button className="lg:hidden p-2 rounded-lg text-foreground/60 hover:text-foreground hover:bg-kgray-light dark:hover:bg-kgray-medium transition-colors">
          <Bars3Icon className="w-5 h-5" />
        </button>

        {/* Search */}
        <div className="flex-1 max-w-lg mx-4">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-foreground/40" />
            </div>
            <input
              type="text"
              placeholder="Search products, orders, customers..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="block w-full pl-12 pr-4 py-3 rounded-xl leading-5 bg-kgray-light/30 dark:bg-kgray-medium/30 placeholder-foreground/50 focus:outline-none focus:ring-2 focus:ring-primary/20 focus:bg-white dark:focus:bg-kgray-medium text-sm transition-all"
            />
          </div>
        </div>

        {/* Right side actions */}
        <div className="flex items-center space-x-3">
          {/* Notifications */}
          <button className="relative p-2.5 text-foreground/60 hover:text-foreground rounded-xl hover:bg-kgray-light dark:hover:bg-kgray-medium transition-colors">
            <BellIcon className="w-5 h-5" />
            <span className="absolute top-1.5 right-1.5 block h-2.5 w-2.5 rounded-full bg-red-500 ring-2 ring-white dark:ring-kgray-dark"></span>
          </button>

          {/* User menu */}
          <div className="relative">
            <button
              onClick={() => setShowUserMenu(!showUserMenu)}
              className="flex items-center space-x-3 p-2 text-sm rounded-xl hover:bg-kgray-light dark:hover:bg-kgray-medium transition-colors"
            >
              <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary-dark rounded-full flex items-center justify-center">
                <span className="text-xs font-semibold text-white">DK</span>
              </div>
              <span className="hidden md:inline text-sm font-medium text-foreground">Dieudonne Kelly</span>
            </button>

            {/* Dropdown Menu */}
            {showUserMenu && (
              <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-kgray-dark rounded-xl py-2 z-50">
                <div className="px-4 py-2">
                  <p className="text-sm font-semibold text-foreground">Dieudonne Kelly</p>
                  <p className="text-xs text-foreground/60">CEO & Founder</p>
                </div>
                <button
                  onClick={handleLogout}
                  className="w-full flex items-center px-4 py-2 text-sm text-foreground/70 hover:text-foreground hover:bg-kgray-light dark:hover:bg-kgray-medium transition-colors"
                >
                  <ArrowRightOnRectangleIcon className="h-4 w-4 mr-3" />
                  Sign out
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
} 