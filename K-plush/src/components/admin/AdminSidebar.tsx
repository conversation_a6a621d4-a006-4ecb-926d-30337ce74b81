'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  HomeIcon, 
  ShoppingBagIcon, 
  ShoppingCartIcon,
  UsersIcon,
  ChartBarIcon,
  CogIcon,
  PaintBrushIcon,
  BellIcon,
  CreditCardIcon,
  TruckIcon,
  MegaphoneIcon
} from '@heroicons/react/24/outline';

const navigation = [
  { name: 'Dashboard', href: '/admin', icon: HomeIcon },
  { name: 'Products', href: '/admin/products', icon: ShoppingBagIcon },
  { name: 'Orders', href: '/admin/orders', icon: ShoppingCartIcon },
  { name: 'Users', href: '/admin/users', icon: UsersIcon },
  { name: 'Custom Designs', href: '/admin/custom-designs', icon: PaintBrushIcon },
  { name: 'Analytics', href: '/admin/analytics', icon: ChartBarIcon },
  { name: 'Payments', href: '/admin/payments', icon: CreditCardIcon },
  { name: 'Shipping', href: '/admin/shipping', icon: TruckIcon },
  { name: 'Marketing', href: '/admin/marketing', icon: MegaphoneIcon },
  { name: 'Notifications', href: '/admin/notifications', icon: BellIcon },
  { name: 'Settings', href: '/admin/settings', icon: CogIcon },
];

export default function AdminSidebar() {
  const pathname = usePathname();

  return (
    <div className="fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-kgray-dark">
      <div className="flex flex-col h-full">
        {/* Logo */}
        <div className="flex items-center justify-center h-16 px-6">
          <Link href="/admin" className="flex items-center space-x-3">
            <div className="w-9 h-9 bg-gradient-to-br from-primary to-primary-dark rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">K</span>
            </div>
            <span className="text-xl font-semibold text-foreground">
              K-Plush Admin
            </span>
          </Link>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
          {navigation.map((item) => {
            const isActive = pathname === item.href || pathname.startsWith(`${item.href}/`);
            return (
              <Link
                key={item.name}
                href={item.href}
                className={`
                  flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 group
                  ${isActive
                    ? 'bg-primary/10 text-primary'
                    : 'text-foreground/70 hover:bg-kgray-light dark:hover:bg-kgray-medium hover:text-foreground'
                  }
                `}
              >
                <item.icon className={`w-5 h-5 mr-3 transition-colors ${
                  isActive ? 'text-primary' : 'text-foreground/50 group-hover:text-foreground/70'
                }`} />
                {item.name}
              </Link>
            );
          })}
        </nav>

        {/* User Info */}
        <div className="p-4">
          <div className="flex items-center space-x-3 p-3 rounded-xl bg-kgray-light/50 dark:bg-kgray-medium/50">
            <div className="w-10 h-10 bg-gradient-to-br from-primary to-primary-dark rounded-full flex items-center justify-center shadow-sm">
              <span className="text-sm font-semibold text-white">DK</span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-semibold text-foreground truncate">
                Dieudonne Kelly
              </p>
              <p className="text-xs text-foreground/60 truncate">
                CEO & Founder
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 