'use client';

import { 
  CurrencyDollarIcon, 
  ShoppingCartIcon, 
  ShoppingBagIcon, 
  UsersIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from '@heroicons/react/24/outline';

// Mock data - replace with real API calls
const stats = [
  {
    name: 'Total Revenue',
    value: 'RWF 2,450,000',
    change: '+20.1%',
    changeType: 'increase',
    icon: CurrencyDollarIcon,
    color: 'from-green-500 to-emerald-600',
    bgColor: 'bg-green-50 dark:bg-green-900/20',
    textColor: 'text-green-600 dark:text-green-400',
  },
  {
    name: 'Total Orders',
    value: '156',
    change: '+15.3%',
    changeType: 'increase',
    icon: ShoppingCartIcon,
    color: 'from-blue-500 to-blue-600',
    bgColor: 'bg-blue-50 dark:bg-blue-900/20',
    textColor: 'text-blue-600 dark:text-blue-400',
  },
  {
    name: 'Active Products',
    value: '89',
    change: '+5.2%',
    changeType: 'increase',
    icon: ShoppingBagIcon,
    color: 'from-purple-500 to-purple-600',
    bgColor: 'bg-purple-50 dark:bg-purple-900/20',
    textColor: 'text-purple-600 dark:text-purple-400',
  },
  {
    name: 'Happy Customers',
    value: '234',
    change: '+12.5%',
    changeType: 'increase',
    icon: UsersIcon,
    color: 'from-orange-500 to-orange-600',
    bgColor: 'bg-orange-50 dark:bg-orange-900/20',
    textColor: 'text-orange-600 dark:text-orange-400',
  },
];

export default function DashboardStats() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat) => (
        <div
          key={stat.name}
          className="bg-white dark:bg-kgray-dark overflow-hidden rounded-2xl transition-all duration-200 group"
        >
          <div className="p-6">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-foreground/60 mb-2">
                  {stat.name}
                </p>
                <p className="text-2xl font-bold text-foreground mb-3">
                  {stat.value}
                </p>
                <div className="flex items-center">
                  {stat.changeType === 'increase' ? (
                    <ArrowUpIcon className="h-4 w-4 text-green-500 mr-1" />
                  ) : (
                    <ArrowDownIcon className="h-4 w-4 text-red-500 mr-1" />
                  )}
                  <span
                    className={`text-sm font-semibold ${
                      stat.changeType === 'increase'
                        ? 'text-green-600 dark:text-green-400'
                        : 'text-red-600 dark:text-red-400'
                    }`}
                  >
                    {stat.change}
                  </span>
                  <span className="text-sm text-foreground/50 ml-1">vs last month</span>
                </div>
              </div>
              <div className={`${stat.bgColor} p-3 rounded-xl group-hover:scale-110 transition-transform duration-200`}>
                <div className={`w-8 h-8 bg-gradient-to-br ${stat.color} rounded-lg flex items-center justify-center`}>
                  <stat.icon className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}