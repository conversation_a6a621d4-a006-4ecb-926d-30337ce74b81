'use client';

import { useState, useRef } from 'react';
import Image from 'next/image';
import { useReactToPrint } from 'react-to-print';
import { 
  PrinterIcon, 
  EnvelopeIcon, 
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline';

// Types for the invoice data
interface InvoiceItem {
  id: number;
  name: string;
  sku: string;
  price: number;
  quantity: number;
}

interface InvoiceAddress {
  street: string;
  city: string;
  state: string;
  zip: string;
  country: string;
}

interface InvoiceCustomer {
  name: string;
  email: string;
  phone: string;
  address: InvoiceAddress;
}

interface InvoicePayment {
  method: string;
  subtotal: number;
  tax: number;
  shipping: number;
  discount: number;
  total: number;
}

interface InvoiceProps {
  orderId: string;
  orderDate: string;
  customer: InvoiceCustomer;
  items: InvoiceItem[];
  payment: InvoicePayment;
  companyInfo?: {
    name: string;
    address: string;
    email: string;
    phone: string;
    website: string;
    logo?: string;
  };
}

// Default company info
const defaultCompanyInfo = {
  name: 'K-plush',
  address: '123 Plush Street, Toyville, TX 75001, United States',
  email: '<EMAIL>',
  phone: '+****************',
  website: 'www.k-plush.com',
  logo: '/images/logo.png'
};

export default function InvoiceGenerator({
  orderId,
  orderDate,
  customer,
  items,
  payment,
  companyInfo = defaultCompanyInfo
}: InvoiceProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const invoiceRef = useRef<HTMLDivElement>(null);

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(date);
  };

  // Generate a random invoice number if not provided
  const invoiceNumber = `INV-${orderId.replace('ORD-', '')}`;

  // Handle print functionality
  const handlePrint = useReactToPrint({
    contentRef: invoiceRef,
    documentTitle: `Invoice-${orderId}`,
  });

  // Handle simple print trigger
  const triggerPrint = () => {
    setIsGenerating(true);
    handlePrint();
    setTimeout(() => setIsGenerating(false), 1000);
  };

  // Handle email invoice (placeholder)
  const handleEmail = () => {
    alert('Email functionality would be implemented here');
  };

  // Handle download as PDF (uses print functionality as a workaround)
  const handleDownload = () => {
    triggerPrint();
  };

  return (
    <div className="space-y-4">
      {/* Action Buttons */}
      <div className="flex flex-wrap gap-3">
        <button
          onClick={triggerPrint}
          disabled={isGenerating}
          className="flex items-center px-4 py-2 bg-blue-600 rounded-md text-sm font-medium text-white hover:bg-blue-700"
        >
          <PrinterIcon className="h-4 w-4 mr-1.5" />
          Print Invoice
        </button>
        <button
          onClick={handleEmail}
          className="flex items-center px-4 py-2 border border-gray-200 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
        >
          <EnvelopeIcon className="h-4 w-4 mr-1.5" />
          Email Invoice
        </button>
        <button
          onClick={handleDownload}
          className="flex items-center px-4 py-2 border border-gray-200 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
        >
          <ArrowDownTrayIcon className="h-4 w-4 mr-1.5" />
          Download PDF
        </button>
      </div>

      {/* Invoice Preview */}
      <div className="bg-white rounded-lg border border-gray-100 p-4">
        <h3 className="text-base font-medium text-gray-900 mb-3">Invoice Preview</h3>
        <div className="border border-gray-200 rounded-md p-2 max-h-96 overflow-y-auto">
          <div className="scale-90 origin-top-left">
            {/* Actual Invoice Content */}
            <div ref={invoiceRef} className="bg-white p-8 max-w-4xl mx-auto">
              {/* Invoice Header */}
              <div className="flex justify-between items-start">
                <div>
                  {companyInfo.logo && (
                    <div className="h-16 w-40 relative mb-4">
                      <Image
                        src={companyInfo.logo}
                        alt={companyInfo.name}
                        fill
                        className="object-contain"
                      />
                    </div>
                  )}
                  <h1 className="text-2xl font-bold text-gray-900">{companyInfo.name}</h1>
                  <p className="text-sm text-gray-500 mt-1">{companyInfo.address}</p>
                  <p className="text-sm text-gray-500">{companyInfo.email}</p>
                  <p className="text-sm text-gray-500">{companyInfo.phone}</p>
                  <p className="text-sm text-gray-500">{companyInfo.website}</p>
                </div>
                <div className="text-right">
                  <div className="inline-block px-4 py-2 bg-blue-50 rounded-md">
                    <h2 className="text-xl font-bold text-gray-900">INVOICE</h2>
                    <p className="text-sm text-gray-500 mt-1">#{invoiceNumber}</p>
                  </div>
                  <div className="mt-4 text-sm text-gray-500">
                    <p><span className="font-medium">Invoice Date:</span> {formatDate(orderDate)}</p>
                    <p><span className="font-medium">Order ID:</span> {orderId}</p>
                  </div>
                </div>
              </div>

              {/* Invoice To */}
              <div className="mt-8">
                <h3 className="text-sm font-medium text-gray-500 uppercase">Invoice To:</h3>
                <div className="mt-2">
                  <p className="text-base font-medium text-gray-900">{customer.name}</p>
                  <p className="text-sm text-gray-500 mt-1">{customer.address.street}</p>
                  <p className="text-sm text-gray-500">
                    {customer.address.city}, {customer.address.state} {customer.address.zip}
                  </p>
                  <p className="text-sm text-gray-500">{customer.address.country}</p>
                  <p className="text-sm text-gray-500 mt-2">{customer.email}</p>
                  <p className="text-sm text-gray-500">{customer.phone}</p>
                </div>
              </div>

              {/* Invoice Items */}
              <div className="mt-8">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Item
                      </th>
                      <th className="py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        SKU
                      </th>
                      <th className="py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Price
                      </th>
                      <th className="py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Qty
                      </th>
                      <th className="py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Total
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {items.map((item) => (
                      <tr key={item.id} className="border-b border-gray-100">
                        <td className="py-4 text-sm text-gray-900">{item.name}</td>
                        <td className="py-4 text-sm text-gray-500">{item.sku}</td>
                        <td className="py-4 text-sm text-gray-900 text-right">
                          ${item.price.toFixed(2)}
                        </td>
                        <td className="py-4 text-sm text-gray-900 text-right">
                          {item.quantity}
                        </td>
                        <td className="py-4 text-sm font-medium text-gray-900 text-right">
                          ${(item.price * item.quantity).toFixed(2)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Invoice Summary */}
              <div className="mt-8 flex justify-end">
                <div className="w-64">
                  <div className="flex justify-between py-2">
                    <span className="text-sm text-gray-500">Subtotal:</span>
                    <span className="text-sm text-gray-900">${payment.subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between py-2">
                    <span className="text-sm text-gray-500">Tax:</span>
                    <span className="text-sm text-gray-900">${payment.tax.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between py-2">
                    <span className="text-sm text-gray-500">Shipping:</span>
                    <span className="text-sm text-gray-900">${payment.shipping.toFixed(2)}</span>
                  </div>
                  {payment.discount > 0 && (
                    <div className="flex justify-between py-2">
                      <span className="text-sm text-gray-500">Discount:</span>
                      <span className="text-sm text-red-600">-${payment.discount.toFixed(2)}</span>
                    </div>
                  )}
                  <div className="flex justify-between py-2 border-t border-gray-200 mt-2">
                    <span className="text-base font-medium text-gray-900">Total:</span>
                    <span className="text-base font-medium text-gray-900">${payment.total.toFixed(2)}</span>
                  </div>
                  <div className="mt-2 py-2 bg-blue-50 rounded-md text-center">
                    <span className="text-sm font-medium text-blue-700">
                      {payment.method}
                    </span>
                  </div>
                </div>
              </div>

              {/* Invoice Footer */}
              <div className="mt-12 pt-4 border-t border-gray-200">
                <div className="text-center text-sm text-gray-500">
                  <p>Thank you for your business!</p>
                  <p className="mt-1">
                    If you have any questions about this invoice, please contact us at {companyInfo.email}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 