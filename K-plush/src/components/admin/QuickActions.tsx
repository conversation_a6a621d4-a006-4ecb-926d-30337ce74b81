'use client';

import Link from 'next/link';
import { 
  PlusIcon, 
  ShoppingBagIcon, 
  UsersIcon, 
  ChartBarIcon,
  PaintBrushIcon,
  CogIcon
} from '@heroicons/react/24/outline';

const quickActions = [
  {
    name: 'Add Product',
    href: '/admin/products/new',
    icon: PlusIcon,
    gradient: 'from-blue-500 to-blue-600',
    bgColor: 'bg-blue-50 dark:bg-blue-900/20',
    description: 'Add new furniture',
  },
  {
    name: 'View Orders',
    href: '/admin/orders',
    icon: ShoppingBagIcon,
    gradient: 'from-green-500 to-emerald-600',
    bgColor: 'bg-green-50 dark:bg-green-900/20',
    description: 'Manage orders',
  },
  {
    name: 'Custom Designs',
    href: '/admin/custom-designs',
    icon: PaintBrushIcon,
    gradient: 'from-purple-500 to-purple-600',
    bgColor: 'bg-purple-50 dark:bg-purple-900/20',
    description: 'Design requests',
  },
  {
    name: 'Analytics',
    href: '/admin/analytics',
    icon: ChartBarIcon,
    gradient: 'from-amber-500 to-orange-600',
    bgColor: 'bg-amber-50 dark:bg-amber-900/20',
    description: 'View insights',
  },
  {
    name: 'Manage Users',
    href: '/admin/users',
    icon: UsersIcon,
    gradient: 'from-pink-500 to-rose-600',
    bgColor: 'bg-pink-50 dark:bg-pink-900/20',
    description: 'Customer management',
  },
  {
    name: 'Settings',
    href: '/admin/settings',
    icon: CogIcon,
    gradient: 'from-gray-500 to-gray-600',
    bgColor: 'bg-gray-50 dark:bg-gray-900/20',
    description: 'Store settings',
  },
];

export default function QuickActions() {
  return (
    <div className="bg-white dark:bg-kgray-dark rounded-2xl">
      <div className="px-6 py-4">
        <h3 className="text-lg font-semibold text-foreground">
          Quick Actions
        </h3>
        <p className="text-sm text-foreground/60 mt-1">
          Frequently used actions
        </p>
      </div>
      <div className="p-4">
        <div className="space-y-3">
          {quickActions.map((action) => (
            <Link
              key={action.name}
              href={action.href}
              className="flex items-center p-4 rounded-xl hover:bg-kgray-light/50 dark:hover:bg-kgray-medium/50 transition-all duration-200 group"
            >
              <div className={`${action.bgColor} p-3 rounded-xl group-hover:scale-110 transition-transform duration-200`}>
                <div className={`w-6 h-6 bg-gradient-to-br ${action.gradient} rounded-md flex items-center justify-center`}>
                  <action.icon className="h-4 w-4 text-white" />
                </div>
              </div>
              <div className="ml-4 flex-1">
                <p className="text-sm font-semibold text-foreground group-hover:text-primary transition-colors">
                  {action.name}
                </p>
                <p className="text-xs text-foreground/60">
                  {action.description}
                </p>
              </div>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-foreground/30 group-hover:text-primary transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
}