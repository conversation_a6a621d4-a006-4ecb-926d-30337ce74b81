'use client';

import Link from 'next/link';
import { EyeIcon, PencilIcon } from '@heroicons/react/24/outline';

const recentOrders = [
  {
    id: 'KP-001',
    customer: '<PERSON>',
    email: '<EMAIL>',
    total: 'RWF 450,000',
    status: 'pending',
    date: '2024-01-15',
    items: 'Living Room Set',
  },
  {
    id: 'KP-002',
    customer: '<PERSON>',
    email: '<EMAIL>',
    total: 'RWF 280,000',
    status: 'processing',
    date: '2024-01-14',
    items: 'Dining Table',
  },
  {
    id: 'KP-003',
    customer: '<PERSON>',
    email: '<EMAIL>',
    total: 'RWF 650,000',
    status: 'shipped',
    date: '2024-01-13',
    items: 'Bedroom Furniture',
  },
];

const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending':
      return 'bg-amber-100 text-amber-800';
    case 'processing':
      return 'bg-blue-100 text-blue-800';
    case 'shipped':
      return 'bg-indigo-100 text-indigo-800';
    case 'delivered':
      return 'bg-green-100 text-green-800';
    case 'custom_design':
      return 'bg-purple-100 text-purple-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export default function RecentOrders() {
  return (
    <div className="bg-white rounded-2xl">
      <div className="px-6 py-4">
        <h3 className="text-lg font-semibold text-gray-900">Recent Orders</h3>
      </div>
      <div className="p-6">
        <div className="space-y-4">
          {recentOrders.map((order) => (
            <div key={order.id} className="flex items-center justify-between p-4 rounded-xl hover:bg-gray-50">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                  <span className="text-sm font-bold text-blue-600">{order.id.split('-')[1]}</span>
                </div>
                <div>
                  <h4 className="text-sm font-semibold text-gray-900">{order.customer}</h4>
                  <p className="text-xs text-gray-500">{order.items}</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(order.status)}`}>
                  {order.status}
                </span>
                <p className="text-sm font-bold text-gray-900">{order.total}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}