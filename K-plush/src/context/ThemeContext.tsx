'use client';

import { createContext, useContext, useEffect, useState } from 'react';

type Theme = 'light' | 'dark';

type ThemeContextType = {
  theme: Theme;
  toggleTheme: () => void;
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);



export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setTheme] = useState<Theme>('light');

  // Initialize theme from localStorage, defaulting to light
  useEffect(() => {
    // Check if user has previously set a theme preference
    const storedTheme = localStorage.getItem('theme') as Theme | null;

    if (storedTheme === 'dark') {
      setTheme('dark');
      document.documentElement.classList.add('dark');
    } else {
      // Default to light theme
      setTheme('light');
      document.documentElement.classList.remove('dark');
      // Ensure light theme is stored
      localStorage.setItem('theme', 'light');
    }

    // Force a re-render to ensure the UI reflects the correct theme
    setTimeout(() => {
      const currentTheme = localStorage.getItem('theme') as Theme;
      console.log('Theme initialized to:', currentTheme, 'Dark class:', document.documentElement.classList.contains('dark'));
    }, 0);
  }, []);

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);

    if (newTheme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }

    localStorage.setItem('theme', newTheme);
    console.log('Theme toggled to:', newTheme, 'Dark class:', document.documentElement.classList.contains('dark'));
  };

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
