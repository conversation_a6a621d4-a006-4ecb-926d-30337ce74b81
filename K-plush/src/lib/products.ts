import { getImageUrl } from './placeholder-images';

export interface Product {
  id: string;
  name: string;
  price: number;
  description: string;
  features: string[];
  category: string;
  image: string;
  images: string[];
  dimensions: {
    width: number;
    height: number;
    depth: number;
  };
  materials: string[];
  inStock: boolean;
}

export const products: Product[] = [
  {
    id: "modern-sofa-1",
    name: "Modern Comfort Sofa",
    price: 1299.99,
    description: "A sleek, modern sofa with clean lines and plush cushions. Perfect for contemporary living spaces.",
    features: [
      "High-density foam cushions",
      "Stain-resistant fabric",
      "Solid wood frame",
      "Removable cushion covers for easy cleaning"
    ],
    category: "Living Room",
    image: getImageUrl('modern-sofa'),
    images: [
      getImageUrl('modern-sofa'),
      getImageUrl('modern-sofa-2'),
      getImageUrl('modern-sofa-3')
    ],
    dimensions: {
      width: 220,
      height: 85,
      depth: 95
    },
    materials: ["Polyester", "Wood", "Metal"],
    inStock: true
  },
  {
    id: "wooden-dining-table-1",
    name: "Artisan Wooden Dining Table",
    price: 899.99,
    description: "Handcrafted dining table made from solid oak. The perfect centerpiece for family gatherings.",
    features: [
      "Solid oak construction",
      "Hand-finished surfaces",
      "Seats 6 people comfortably",
      "Scratch-resistant coating"
    ],
    category: "Dining",
    image: getImageUrl('dining-table'),
    images: [
      getImageUrl('dining-table'),
      getImageUrl('dining-table-2'),
      getImageUrl('dining-table-3')
    ],
    dimensions: {
      width: 180,
      height: 75,
      depth: 90
    },
    materials: ["Oak", "Natural Oil Finish"],
    inStock: true
  },
  {
    id: "luxury-bed-frame-1",
    name: "Luxury Platform Bed Frame",
    price: 1499.99,
    description: "A modern platform bed frame with integrated storage and upholstered headboard.",
    features: [
      "Upholstered headboard",
      "Under-bed storage drawers",
      "Solid wood slats (no box spring needed)",
      "Noise-free design"
    ],
    category: "Bedroom",
    image: getImageUrl('bed-frame'),
    images: [
      getImageUrl('bed-frame'),
      getImageUrl('bed-frame-2'),
      getImageUrl('bed-frame-3')
    ],
    dimensions: {
      width: 165,
      height: 120,
      depth: 215
    },
    materials: ["Wood", "Linen", "Metal"],
    inStock: true
  },
  {
    id: "accent-chair-1",
    name: "Scandinavian Accent Chair",
    price: 499.99,
    description: "A beautiful accent chair inspired by Scandinavian design principles of simplicity and functionality.",
    features: [
      "Ergonomic design",
      "High-quality fabric upholstery",
      "Solid beech wood legs",
      "Foam padding for comfort"
    ],
    category: "Living Room",
    image: getImageUrl('accent-chair'),
    images: [
      getImageUrl('accent-chair'),
      getImageUrl('accent-chair-2'),
      getImageUrl('accent-chair-3')
    ],
    dimensions: {
      width: 70,
      height: 80,
      depth: 75
    },
    materials: ["Beech Wood", "Polyester", "Foam"],
    inStock: true
  },
  {
    id: "coffee-table-1",
    name: "Minimalist Coffee Table",
    price: 349.99,
    description: "A minimalist coffee table with clean lines and a marble top, perfect for modern living rooms.",
    features: [
      "Genuine marble top",
      "Solid metal base",
      "Protective floor pads",
      "Easy assembly"
    ],
    category: "Living Room",
    image: getImageUrl('coffee-table'),
    images: [
      getImageUrl('coffee-table'),
      getImageUrl('coffee-table-2'),
      getImageUrl('coffee-table-3')
    ],
    dimensions: {
      width: 120,
      height: 45,
      depth: 60
    },
    materials: ["Marble", "Steel"],
    inStock: true
  },
  {
    id: "bookshelf-1",
    name: "Modern Bookshelf",
    price: 599.99,
    description: "A spacious modern bookshelf with an open design, perfect for displaying books and decorative items.",
    features: [
      "Multiple shelves for ample storage",
      "Sturdy construction",
      "Anti-tip safety feature",
      "Adjustable shelves"
    ],
    category: "Living Room",
    image: getImageUrl('bookshelf'),
    images: [
      getImageUrl('bookshelf'),
      getImageUrl('bookshelf-2'),
      getImageUrl('bookshelf-3')
    ],
    dimensions: {
      width: 100,
      height: 180,
      depth: 35
    },
    materials: ["Engineered Wood", "Metal"],
    inStock: true
  }
];

export function getProductById(id: string): Product | undefined {
  return products.find(product => product.id === id);
}

export function getProductsByCategory(category: string): Product[] {
  return products.filter(product =>
    product.category.toLowerCase() === category.toLowerCase()
  );
}

export function getAllCategories(): string[] {
  const categories = new Set(products.map(product => product.category));
  return Array.from(categories);
}
