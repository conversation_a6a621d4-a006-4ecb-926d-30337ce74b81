import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  // We'll handle admin routes in a separate layout
  const { pathname } = request.nextUrl;
  
  if (pathname.startsWith('/admin')) {
    // For admin routes, we'll use a special header to identify them
    const requestHeaders = new Headers(request.headers);
    requestHeaders.set('x-is-admin-route', '1');
    
    return NextResponse.next({
      request: {
        headers: requestHeaders,
      },
    });
  }
  
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}; 